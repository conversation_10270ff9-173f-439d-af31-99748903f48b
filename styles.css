/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Web3 Colors - Optimized for Contrast */
  --primary-color: #00D4FF;
  --primary-dark: #0099CC;
  --primary-light: #33DDFF;
  --secondary-color: #8B5CF6;
  --secondary-light: #A78BFA;
  --accent-color: #10B981;
  --accent-dark: #059669;
  --background-dark: #0B0B0F;
  --background-card: #1C1C23;
  --background-section: #141419;
  --text-color: #FFFFFF;
  --text-light: #D1D5DB;
  --text-muted: #9CA3AF;
  --text-dim: #6B7280;
  --white: #ffffff;
  --gray-50: #1F1F23;
  --gray-100: #2A2A30;
  --gray-200: #404047;
  --gray-300: #52525B;
  --gray-900: #0F0F14;

  /* Status Colors */
  --success-color: #10B981;
  --error-color: #EF4444;
  --warning-color: #F59E0B;
  
  /* Typography */
  --font-family: 'Inter', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  /* Layout */
  --container-max-width: 1200px;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Transitions */
  --transition: all 0.3s ease;
  --transition-fast: all 0.15s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-dark);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
}

.btn--primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn--primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}

.btn--secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn--secondary:hover {
  background-color: var(--primary-color);
  color: var(--background-dark);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.btn--full {
  width: 100%;
}

.section__header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
}

.section__title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-light);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(11, 11, 15, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  z-index: 1000;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
}

.nav__logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo__text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
}

.logo__tagline {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  font-weight: var(--font-weight-normal);
  margin-top: 2px;
}

.nav__link--cta {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark) !important;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
  transition: var(--transition);
}

.nav__link--cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
}

.nav__list {
  display: flex;
  gap: var(--spacing-xl);
}

.nav__link {
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  transition: var(--transition);
}

.nav__link:hover {
  color: var(--primary-color);
}

.nav__toggle {
  display: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--text-color);
}

/* ===== HERO SECTION ===== */
.hero {
  padding: calc(80px + var(--spacing-4xl)) 0 var(--spacing-4xl);
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, rgba(139, 92, 246, 0.05) 50%, var(--background-dark) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero__badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--spacing-2xl);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.hero__title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__description {
  font-size: var(--font-size-lg);
  color: var(--text-light);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
  max-width: 90%;
}

.hero__features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.feature-highlight {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-color);
}

.feature-highlight i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  margin-top: 2px;
  flex-shrink: 0;
}

.feature-highlight span {
  color: var(--text-light);
  font-size: var(--font-size-base);
  line-height: 1.5;
}

.hero__cta {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.hero__tech {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-lg);
}

.hero__tech i {
  color: var(--accent-color);
}

.hero__counter {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.counter__number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
}

.counter__label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.hero__buttons {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.hero__cta, .hero__demo {
  font-size: var(--font-size-lg);
  padding: var(--spacing-md) var(--spacing-2xl);
}

.hero__powered {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
}

.hero__stats {
  display: flex;
  gap: var(--spacing-xl);
}

.stat {
  text-align: center;
}

.stat__number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.stat__label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  opacity: 0.8;
}

/* Hero Visual - Architecture Stack */
.hero__visual {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 600px;
  position: relative;
  overflow: visible;
  padding: var(--spacing-xl);
}

.architecture-stack {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: var(--spacing-xl);
}

.stack-layer {
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: var(--transition);
  position: relative;
}

.stack-layer:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.wallet-layer {
  border: 2px solid var(--primary-color);
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.05));
}

.fluent-layer {
  border: 2px solid var(--secondary-color);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(0, 212, 255, 0.05));
}

.chains-layer {
  border: 2px solid var(--accent-color);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(139, 92, 246, 0.05));
}

.layer-header {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.zkd-wallet {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.zkd-wallet i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
}

.zkd-wallet span {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.wallet-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--accent-color);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--accent-color);
  border-radius: 50%;
  animation: statusBlink 2s ease-in-out infinite;
}

.wallet-capabilities {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-sm);
}

.capability {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  flex: 1;
  text-align: center;
}

.capability i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.capability span {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.fluent-core {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.fluent-core i {
  font-size: var(--font-size-2xl);
  color: var(--secondary-color);
}

.fluent-core span {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.compilation-status {
  display: flex;
  gap: var(--spacing-xs);
}

.compile-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: compilePulse 1.5s ease-in-out infinite;
}

.compile-indicator.rust {
  background: #CE422B;
}

.compile-indicator.solidity {
  background: #627EEA;
}

@keyframes compilePulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

.compilation-engines {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-lg);
  margin: var(--spacing-md) 0;
}

.compiler {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  flex: 1;
  text-align: center;
}

.rust-compiler i {
  color: #CE422B;
}

.solidity-compiler i {
  color: #627EEA;
}

.compile-arrow {
  font-size: var(--font-size-lg);
  color: var(--secondary-color);
  animation: bounce 2s ease-in-out infinite;
}

.vm-outputs {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-sm);
}

/* WASM Layer Styles */
.wasm-layer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin: var(--spacing-md) 0;
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.wasm-compilation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.1), rgba(255, 140, 0, 0.05));
  border: 1px solid #FF8C00;
  border-radius: var(--border-radius);
  text-align: center;
}

.wasm-compilation i {
  color: #FF8C00;
  font-size: var(--font-size-lg);
}

.rwasm-core {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: linear-gradient(135deg, rgba(0, 255, 127, 0.1), rgba(0, 255, 127, 0.05));
  border: 1px solid #00FF7F;
  border-radius: var(--border-radius);
  text-align: center;
}

.rwasm-core i {
  color: #00FF7F;
  font-size: var(--font-size-lg);
}

/* Compatibility Layer Styles */
.compatibility-layer {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.compatibility-contract {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  text-align: center;
  flex: 1;
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.evm-compat {
  border: 1px solid #627EEA;
}

.evm-compat i {
  color: #627EEA;
}

.svm-compat {
  border: 1px solid #9945FF;
}

.svm-compat i {
  color: #9945FF;
}

.vm-output {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.08);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  color: var(--text-light);
  text-align: center;
  flex: 1;
}

.evm-output {
  border: 1px solid #627EEA;
}

.svm-output {
  border: 1px solid #9945FF;
}

.layer-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  text-align: center;
}

.target-chains {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.chain-group {
  display: flex;
  justify-content: space-around;
  gap: var(--spacing-sm);
}

.chain {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  flex: 1;
  text-align: center;
}

.chain:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.chain.ethereum i {
  color: #627EEA;
}

.chain.solana i {
  color: #9945FF;
}

.chain.polygon i {
  color: #8247E5;
}

.chain.arbitrum i {
  color: #28A0F0;
}

.chain.eclipse i {
  color: #FF6B35;
}

.chain.neon i {
  color: #00D4FF;
}

.chain span {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.stack-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.connection-svg {
  width: 100%;
  height: 100%;
}

.connection-path {
  fill: none;
  stroke: var(--primary-color);
  stroke-width: 2;
  opacity: 0.6;
}

.connection-path.primary {
  stroke: var(--primary-color);
  stroke-width: 3;
}

.connection-path.secondary {
  stroke: var(--secondary-color);
  stroke-width: 2;
}

.flow-dot {
  fill: var(--primary-color);
}

.floating-dapps {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.dapp-icon {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: float 6s ease-in-out infinite;
}

.dapp-icon i {
  color: var(--primary-color);
  font-size: var(--font-size-sm);
}

.dapp-icon.uniswap {
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.dapp-icon.opensea {
  top: 30%;
  left: 5%;
  animation-delay: 1.5s;
}

.dapp-icon.pump {
  bottom: 30%;
  right: 5%;
  animation-delay: 3s;
}

.dapp-icon.curve {
  bottom: 10%;
  left: 15%;
  animation-delay: 4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.universal-connection-demo {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.demo-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.wallet-icon {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius-lg);
  color: var(--background-dark);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
  animation: walletPulse 3s ease-in-out infinite;
}

@keyframes walletPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.5);
  }
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--accent-color);
  font-size: var(--font-size-sm);
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--accent-color);
  border-radius: 50%;
  animation: statusBlink 2s ease-in-out infinite;
}

@keyframes statusBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.connection-flow {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  width: 100%;
  max-width: 500px;
  position: relative;
}

.blockchain-layer,
.fluent-layer,
.dapp-layer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
}

.blockchain {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.blockchain:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.blockchain.ethereum i {
  color: #627EEA;
}

.blockchain.solana i {
  color: #9945FF;
}

.blockchain.polygon i {
  color: #8247E5;
}

.fluent-core {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(0, 212, 255, 0.2));
  border: 2px solid var(--secondary-color);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(15px);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
}

.vm-indicators {
  display: flex;
  gap: var(--spacing-sm);
}

.vm {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.vm.evm {
  border: 1px solid #627EEA;
}

.vm.svm {
  border: 1px solid #9945FF;
}

.dapp-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.dapp-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: var(--transition);
}

.dapp-item:hover {
  background: rgba(255, 255, 255, 0.06);
  transform: translateY(-2px);
}

.dapp-item i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.dapp-item span {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.connection-svg {
  width: 100%;
  height: 100%;
}

.connection-path {
  fill: none;
  stroke: var(--primary-color);
  stroke-width: 2;
  opacity: 0.6;
  stroke-dasharray: 5, 5;
  animation: connectionFlow 3s ease-in-out infinite;
}

@keyframes connectionFlow {
  0% {
    stroke-dashoffset: 0;
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    stroke-dashoffset: -20;
    opacity: 0.3;
  }
}

.dapps-showcase {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xl);
}

.dapps-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  width: 100%;
  max-width: 600px;
  margin-bottom: var(--spacing-lg);
}

.zkd-hub {
  position: relative;
  z-index: 10;
}

.hub-core {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(0, 212, 255, 0.2));
  border: 2px solid rgba(139, 92, 246, 0.5);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(15px);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
  animation: hubPulse 3s ease-in-out infinite;
}

@keyframes hubPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 25px 50px rgba(139, 92, 246, 0.4);
  }
}

.hub-label {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--secondary-color);
  text-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
}

.hub-subtitle {
  font-size: var(--font-size-base);
  color: var(--primary-color);
  opacity: 0.9;
}

.hub-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.dapp-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.connection-line {
  position: absolute;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  width: 2px;
  opacity: 0.6;
  animation: connectionPulse 3s ease-in-out infinite;
}

.connection-line.line-1 {
  left: 12.5%;
  top: 15%;
  height: 25%;
  animation-delay: 0s;
}

.connection-line.line-2 {
  left: 25%;
  top: 15%;
  height: 25%;
  animation-delay: 0.2s;
}

.connection-line.line-3 {
  left: 37.5%;
  top: 15%;
  height: 25%;
  animation-delay: 0.4s;
}

.connection-line.line-4 {
  left: 50%;
  top: 15%;
  height: 25%;
  animation-delay: 0.6s;
}

.connection-line.line-5 {
  left: 62.5%;
  top: 15%;
  height: 25%;
  animation-delay: 0.8s;
}

.connection-line.line-6 {
  left: 75%;
  top: 15%;
  height: 25%;
  animation-delay: 1s;
}

.connection-line.line-7 {
  left: 87.5%;
  top: 15%;
  height: 25%;
  animation-delay: 1.2s;
}

@keyframes connectionPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleY(1.1);
  }
}

.hero-data-packets {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.hero-packet {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--accent-color);
  animation: heroDataFlow 4s ease-in-out infinite;
}

.hero-packet.packet-1 {
  left: 12.5%;
  animation-delay: 0s;
}

.hero-packet.packet-2 {
  left: 37.5%;
  animation-delay: 1s;
}

.hero-packet.packet-3 {
  left: 62.5%;
  animation-delay: 2s;
}

.hero-packet.packet-4 {
  left: 87.5%;
  animation-delay: 3s;
}

@keyframes heroDataFlow {
  0% {
    top: 10%;
    opacity: 0;
    transform: scale(0.5);
  }
  25% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    top: 40%;
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  100% {
    top: 70%;
    opacity: 0;
    transform: scale(0.5);
  }
}

.tech-flow-diagram {
  position: relative;
  width: 100%;
  max-width: 420px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  gap: var(--spacing-md);
}

/* Flow Layers */
.flow-layer {
  position: relative;
  z-index: 2;
}

.flow-layer--dapps {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.dapp-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--background-card);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  cursor: pointer;
  animation: dappPulse 3s ease-in-out infinite;
  min-width: 70px;
}

.dapp-node:nth-child(1) { animation-delay: 0s; }
.dapp-node:nth-child(2) { animation-delay: 1s; }
.dapp-node:nth-child(3) { animation-delay: 2s; }

@keyframes dappPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(0, 212, 255, 0);
  }
}

.dapp-icon {
  font-size: 24px;
  margin-bottom: var(--spacing-xs);
}

.dapp-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
}

/* Provider Core */
.flow-layer--provider {
  display: flex;
  justify-content: center;
  margin: var(--spacing-md) 0;
  flex-shrink: 0;
}

.provider-core {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  text-align: center;
  color: var(--background-dark);
  box-shadow: 0 0 40px rgba(0, 212, 255, 0.4);
  animation: providerGlow 2s ease-in-out infinite alternate;
  position: relative;
  overflow: hidden;
  max-width: 300px;
  width: 100%;
}

@keyframes providerGlow {
  0% { box-shadow: 0 0 40px rgba(0, 212, 255, 0.4); }
  100% { box-shadow: 0 0 60px rgba(0, 212, 255, 0.6); }
}

.provider-header {
  margin-bottom: var(--spacing-sm);
}

.provider-label {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
}

.provider-subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.tech-stack {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
  margin: var(--spacing-sm) 0;
}

.tech-item {
  background: rgba(0, 0, 0, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.provider-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: var(--accent-color);
  border-radius: 50%;
  animation: statusBlink 1.5s ease-in-out infinite;
}

@keyframes statusBlink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.status-text {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* Fluent L2 Layer */
.flow-layer--fluent {
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

.fluent-container {
  background: var(--background-card);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  text-align: center;
  width: 100%;
  max-width: 320px;
}

.fluent-header {
  margin-bottom: var(--spacing-md);
}

.fluent-label {
  display: block;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.fluent-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.vm-containers {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

.vm-box {
  flex: 1;
  background: var(--background-dark);
  border: 2px solid;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm);
  text-align: center;
  transition: all 0.3s ease;
  animation: vmPulse 4s ease-in-out infinite;
  min-width: 100px;
}

.vm-evm {
  border-color: var(--primary-color);
  animation-delay: 0s;
}

.vm-svm {
  border-color: var(--accent-color);
  animation-delay: 2s;
}

@keyframes vmPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 8px rgba(0, 212, 255, 0);
  }
}

.vm-icon {
  font-size: 20px;
  margin-bottom: var(--spacing-xs);
}

.vm-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.vm-desc {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

/* Flow Connections */
.flow-connections, .output-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.flow-line, .output-line {
  position: absolute;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
  width: 2px;
  opacity: 0.6;
  animation: flowPulse 2s ease-in-out infinite;
}

.flow-line--1 {
  left: 20%;
  top: 25%;
  height: 20%;
  animation-delay: 0s;
}

.flow-line--2 {
  left: 50%;
  top: 25%;
  height: 20%;
  animation-delay: 0.3s;
}

.flow-line--3 {
  left: 80%;
  top: 25%;
  height: 20%;
  animation-delay: 0.6s;
}

.output-line--evm {
  left: 40%;
  top: 55%;
  height: 20%;
  animation-delay: 1s;
}

.output-line--svm {
  left: 60%;
  top: 55%;
  height: 20%;
  animation-delay: 1.3s;
}

@keyframes flowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleY(1.1);
  }
}

/* Animated Data Packets */
.data-packets {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.data-packet {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--accent-color);
  animation: dataFlow 3s ease-in-out infinite;
}

.packet-1 {
  left: 20%;
  animation-delay: 0s;
}

.packet-2 {
  left: 50%;
  animation-delay: 1s;
}

.packet-3 {
  left: 80%;
  animation-delay: 2s;
}

@keyframes dataFlow {
  0% {
    top: 20%;
    opacity: 0;
    transform: scale(0.5);
  }
  25% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    top: 45%;
    opacity: 1;
  }
  75% {
    opacity: 1;
  }
  100% {
    top: 75%;
    opacity: 0;
    transform: scale(0.5);
  }
}

/* ===== PROBLEM/SOLUTION SECTION ===== */
.problem-solution {
  padding: var(--spacing-4xl) 0;
  background: var(--background-section);
}

.problem__title {
  font-size: var(--font-size-4xl) !important;
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.problem-solution__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  max-width: 1000px;
  margin: 0 auto;
}

.problem__side, .solution__side {
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
}

.problem__side {
  background: rgba(239, 68, 68, 0.08);
  border: 1px solid rgba(239, 68, 68, 0.3);
  backdrop-filter: blur(10px);
}

.solution__side {
  background: rgba(16, 185, 129, 0.08);
  border: 1px solid rgba(16, 185, 129, 0.3);
  backdrop-filter: blur(10px);
}

.solution__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--accent-color);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.problem__item, .solution__item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

.problem__icon, .solution__icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.problem__text, .solution__text {
  color: var(--text-light);
  font-size: var(--font-size-base);
  line-height: 1.5;
}

/* ===== DEMO SECTION ===== */
.demo {
  padding: var(--spacing-4xl) 0;
  background: var(--background-dark);
}

.demo__content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
}

.demo__video {
  position: relative;
}

.video-placeholder {
  background: linear-gradient(135deg, var(--background-card) 0%, var(--gray-100) 100%);
  border-radius: var(--border-radius-lg);
  aspect-ratio: 16/9;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--gray-200);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.video-play-button {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--background-dark);
  font-size: var(--font-size-2xl);
  cursor: pointer;
  transition: var(--transition);
  z-index: 2;
}

.video-play-button:hover {
  transform: scale(1.1);
}

.video-overlay {
  position: absolute;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  color: var(--text-color);
}

.video-overlay h3 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xs);
}

.video-overlay p {
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

.demo__steps {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.demo__step {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--background-card);
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
}

.step__number {
  width: 30px;
  height: 30px;
  background: var(--primary-color);
  color: var(--background-dark);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.step__text {
  color: var(--text-light);
  line-height: 1.4;
}

/* ===== HOW IT WORKS SECTION ===== */
.how-it-works {
  padding: var(--spacing-4xl) 0;
  background: var(--background-section);
}

.how-it-works__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-2xl);
}

.tech__card {
  background: var(--background-card);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  text-align: center;
  transition: var(--transition);
}

.tech__card:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
  background: var(--gray-100);
}

.tech__number {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-md);
}

.tech__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

.tech__description {
  color: var(--text-light);
  line-height: 1.6;
}

/* ===== TECHNICAL ARCHITECTURE SECTION ===== */
.tech-architecture {
  padding: var(--spacing-4xl) 0;
  background: var(--background-dark);
}

.architecture__visual {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-2xl) 0;
}

.architecture__visual .tech-flow-diagram {
  max-width: 500px;
  height: 400px;
  margin: 0 auto;
}

.explanation__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-3xl);
}

.explanation__item {
  background: var(--background-card);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  text-align: center;
  transition: var(--transition);
}

.explanation__item:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
  background: var(--gray-100);
}

.explanation__icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-md);
}

.explanation__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

.explanation__text {
  color: var(--text-light);
  line-height: 1.6;
}

/* ===== SOCIAL PROOF SECTION ===== */
.social-proof {
  padding: var(--spacing-4xl) 0;
  background: var(--background-dark);
}

.testimonials__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-3xl);
}

.testimonial__card {
  background: var(--background-card);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  transition: var(--transition);
  position: relative;
}

.testimonial__card:hover {
  transform: translateY(-3px);
  border-color: var(--primary-color);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
  background: var(--gray-100);
}

.testimonial__verified {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--accent-color);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-md);
}

.testimonial__text {
  font-size: var(--font-size-lg);
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
  font-style: italic;
}

.testimonial__author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.testimonial__avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--background-dark);
  font-weight: var(--font-weight-bold);
}

.avatar__initial {
  font-size: var(--font-size-lg);
}

.testimonial__name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.testimonial__title {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.social-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-3xl);
  flex-wrap: wrap;
}

.social-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-lg);
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  min-width: 150px;
}

.social-stat i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
}

.social-stat .stat__number {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.social-stat .stat__label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  opacity: 0.9;
}

/* ===== WAITLIST SECTION ===== */
.waitlist {
  padding: var(--spacing-4xl) 0;
  background: linear-gradient(135deg, var(--background-section) 0%, var(--background-dark) 100%);
  position: relative;
}

.waitlist__content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.waitlist__benefits {
  margin-bottom: var(--spacing-3xl);
}

.benefits__title {
  font-size: var(--font-size-xl);
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
}

.benefit__item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.benefit__icon {
  font-size: var(--font-size-xl);
}

.benefit__text {
  color: var(--text-light);
}

.waitlist__form {
  margin-bottom: var(--spacing-3xl);
}

.waitlist__form .form__group {
  display: flex;
  gap: var(--spacing-md);
  max-width: 500px;
  margin: 0 auto;
}

.waitlist__form .form__input {
  flex: 1;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  background: var(--background-card);
  color: var(--text-color);
}

.waitlist__form .form__input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.waitlist__btn {
  white-space: nowrap;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border: none;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); }
  to { box-shadow: 0 0 30px rgba(0, 212, 255, 0.6); }
}

.waitlist__stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
  flex-wrap: wrap;
}

.waitlist__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-lg);
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
  min-width: 200px;
}

.stat__icon {
  font-size: var(--font-size-xl);
}

.waitlist__stat .stat__number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.waitlist__stat .stat__label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  opacity: 0.9;
}

.referral__info {
  background: var(--background-card);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--gray-200);
}

.referral__title {
  font-size: var(--font-size-lg);
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
}

.referral__tiers {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.referral__tier {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background: rgba(0, 212, 255, 0.1);
  border-radius: var(--border-radius);
  min-width: 150px;
}

.tier__count {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.tier__reward {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  text-align: center;
  opacity: 0.9;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--background-dark);
  color: var(--white);
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
}

.footer__content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: var(--spacing-2xl);
  align-items: start;
  margin-bottom: var(--spacing-xl);
}

.footer__logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer__description {
  color: var(--text-light);
  max-width: 300px;
  line-height: 1.6;
}

.footer__section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer__title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

.footer__link {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  transition: var(--transition);
}

.footer__link:hover {
  color: var(--primary-color);
}

.footer__social {
  display: flex;
  gap: var(--spacing-md);
}

.footer__social-link {
  width: 40px;
  height: 40px;
  background-color: var(--background-card);
  border: 1px solid var(--gray-200);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  transition: var(--transition);
}

.footer__social-link:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--background-dark);
  transform: translateY(-2px);
}

.footer__bottom {
  text-align: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
}

.footer__copyright {
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles (768px - 1199px) */
@media screen and (max-width: 1199px) {
  .container {
    padding: 0 var(--spacing-lg);
  }

  .hero__container {
    gap: var(--spacing-2xl);
  }

  .hero__title {
    font-size: var(--font-size-4xl);
  }

  .section__title {
    font-size: var(--font-size-2xl);
  }

  .features__grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .testimonials__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Styles (320px - 767px) */
@media screen and (max-width: 767px) {
  /* Navigation */
  .nav__menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-card) 100%);
    transition: var(--transition);
    border-right: 1px solid var(--gray-200);
    backdrop-filter: blur(20px);
  }

  .nav__menu.show-menu {
    left: 0;
  }

  .nav__list {
    flex-direction: column;
    padding: var(--spacing-2xl);
    gap: var(--spacing-lg);
  }

  .nav__link {
    font-size: var(--font-size-lg);
  }

  .nav__toggle {
    display: block;
  }

  /* Hero Section */
  .hero {
    padding: calc(70px + var(--spacing-2xl)) 0 var(--spacing-2xl);
  }

  .hero__container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero__title {
    font-size: var(--font-size-3xl);
  }

  .hero__subtitle {
    font-size: var(--font-size-lg);
  }

  /* Mobile hero dApps showcase optimizations */
  .hero__visual {
    height: 350px;
    padding: var(--spacing-sm);
  }

  .dapps-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    max-width: 300px;
  }

  .dapp-node {
    padding: var(--spacing-xs);
  }

  .dapp-icon {
    font-size: 1.2rem;
  }

  .dapp-label {
    font-size: 0.7rem;
  }

  .hub-core {
    padding: var(--spacing-md);
  }

  .hub-label {
    font-size: var(--font-size-lg);
  }

  .hub-subtitle {
    font-size: var(--font-size-sm);
  }

  /* Mobile tech flow optimizations */
  .tech-flow-diagram {
    max-width: 100%;
    gap: var(--spacing-sm);
  }

  .provider-core {
    max-width: 280px;
    padding: var(--spacing-sm);
  }

  .fluent-container {
    max-width: 280px;
    padding: var(--spacing-sm);
  }

  .vm-box {
    min-width: 80px;
    padding: var(--spacing-xs);
  }

  /* Technical Architecture Section */
  .explanation__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .explanation__item {
    padding: var(--spacing-xl);
  }

  /* Sections */
  .section__title {
    font-size: var(--font-size-2xl);
  }

  .section__subtitle {
    font-size: var(--font-size-base);
  }

  /* Hero buttons */
  .hero__buttons {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .hero__stats {
    justify-content: center;
  }

  /* Problem/Solution */
  .problem-solution__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  /* Demo */
  .demo__content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  /* How It Works */
  .how-it-works__grid {
    grid-template-columns: 1fr;
  }

  .tech__card {
    padding: var(--spacing-xl);
  }

  /* Social Proof */
  .testimonials__grid {
    grid-template-columns: 1fr;
  }

  .testimonial__card {
    padding: var(--spacing-xl);
  }

  .social-stats {
    gap: var(--spacing-lg);
  }

  /* Waitlist */
  .waitlist__form .form__group {
    flex-direction: column;
  }

  .waitlist__stats {
    gap: var(--spacing-lg);
  }

  .referral__tiers {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  /* Footer */
  .footer__content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }

  .footer__social {
    justify-content: center;
  }
}

/* Small Mobile Styles (320px - 479px) */
@media screen and (max-width: 479px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .hero__title {
    font-size: var(--font-size-2xl);
  }

  .hero__subtitle {
    font-size: var(--font-size-base);
  }

  .btn {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .hero__cta {
    font-size: var(--font-size-base);
    padding: var(--spacing-md) var(--spacing-xl);
  }

  .wallet-core {
    width: 80px;
    height: 80px;
    font-size: var(--font-size-lg);
  }

  .chain-orbit {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-xs);
  }

  .testimonial__text {
    font-size: var(--font-size-base);
  }

  .waitlist__stat {
    min-width: 150px;
  }

  /* Extra small mobile hero dApps showcase optimizations */
  .hero__visual {
    height: 300px;
    padding: var(--spacing-xs);
  }

  .dapps-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xs);
    max-width: 250px;
  }

  .dapp-node {
    padding: 4px var(--spacing-xs);
  }

  .dapp-icon {
    font-size: 1rem;
  }

  .dapp-label {
    font-size: 0.6rem;
  }

  .hub-core {
    padding: var(--spacing-sm);
  }

  .hub-label {
    font-size: var(--font-size-base);
  }

  .hub-subtitle {
    font-size: var(--font-size-xs);
  }

  /* Extra small mobile tech flow optimizations */
  .tech-flow-diagram {
    gap: var(--spacing-xs);
  }

  .provider-core {
    max-width: 250px;
    padding: var(--spacing-xs);
  }

  .fluent-container {
    max-width: 250px;
    padding: var(--spacing-xs);
  }

  .vm-box {
    min-width: 70px;
    padding: 4px;
  }

  .vm-icon {
    font-size: 16px;
  }
}

/* ===== ACCESSIBILITY & PERFORMANCE ===== */

/* Focus styles for keyboard navigation */
.btn:focus,
.nav__link:focus,
.form__input:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #00FFFF;
    --secondary-color: #FFFFFF;
    --text-color: #FFFFFF;
    --text-light: #E5E5E5;
    --background-dark: #000000;
    --background-card: #1A1A1A;
  }

  .section__title {
    background: none !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    color: var(--text-color) !important;
  }
}

/* Performance optimizations */
.floating-wallet,
.chain-orbit,
.video-play-button {
  will-change: transform;
}

.hero__badge,
.waitlist__btn {
  will-change: transform, box-shadow;
}

/* Subtle text shadows for better readability */
.hero__title,
.section__title {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero__subtitle,
.section__subtitle {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Enhanced focus states for better accessibility */
.btn:focus-visible,
.nav__link:focus-visible,
.form__input:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(0, 212, 255, 0.2);
}

/* Print styles */
@media print {
  .header,
  .nav__toggle,
  .footer__social,
  .btn,
  .floating-wallet {
    display: none;
  }

  .hero,
  .problem-solution,
  .demo,
  .how-it-works,
  .social-proof,
  .waitlist {
    padding: var(--spacing-lg) 0;
  }

  .hero__container,
  .demo__content,
  .problem-solution__grid {
    grid-template-columns: 1fr;
  }

  * {
    background: white !important;
    color: black !important;
  }
}

/* ===== NEW SECTIONS STYLES ===== */

/* ===== HOW IT WORKS SECTION ===== */
.how-it-works {
  padding: var(--spacing-4xl) 0;
  background: var(--background-section);
  position: relative;
}

.how-it-works::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.how-it-works__content {
  position: relative;
  z-index: 1;
}

.work-step {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
  margin-bottom: var(--spacing-4xl);
  padding: var(--spacing-2xl) 0;
}

.work-step--reverse {
  grid-template-columns: 1fr 1fr;
}

.work-step--reverse .step__visual {
  order: 2;
}

.work-step--reverse .step__content {
  order: 1;
}

.step__visual {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.step__diagram {
  width: 100%;
  max-width: 400px;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.step__content {
  padding: var(--spacing-lg);
}

.step__number {
  display: inline-flex;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.step__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.step__description {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.step__features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feature-point {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.feature-point i {
  color: var(--accent-color);
  font-size: var(--font-size-sm);
}

.feature-point span {
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

/* Step Diagrams */
.smart-account-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.account-core {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(139, 92, 246, 0.2));
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(15px);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
}

.account-core i {
  font-size: var(--font-size-3xl);
  color: var(--primary-color);
}

.account-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-item i {
  color: var(--accent-color);
}

.provider-injection-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.browser-window {
  width: 100%;
  max-width: 300px;
  background: var(--background-card);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.browser-header {
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.browser-content {
  padding: var(--spacing-lg);
  text-align: center;
}

.dapp-interface {
  padding: var(--spacing-md);
  background: var(--primary-color);
  color: var(--background-dark);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
}

.injection-arrow {
  font-size: var(--font-size-2xl);
  color: var(--accent-color);
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.zkd-extension {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
  color: var(--background-dark);
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
}

.cross-chain-visual {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  align-items: center;
}

.transaction-flow {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.tx-start,
.tx-fluent,
.tx-target {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tx-fluent {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(0, 212, 255, 0.2));
  border: 2px solid var(--secondary-color);
}

.tx-arrow {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  animation: flowPulse 2s ease-in-out infinite;
}

@keyframes flowPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.execution-details {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  justify-content: center;
}

.detail-item {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid var(--accent-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  color: var(--accent-color);
}

/* ===== ARCHITECTURE SECTION ===== */
.architecture {
  padding: var(--spacing-4xl) 0;
  background: var(--background-dark);
  position: relative;
}

.architecture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.architecture__content {
  position: relative;
  z-index: 1;
}

.architecture__diagram {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-4xl);
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  position: relative;
}

.arch-layer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.arch-layer:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.arch-layer--user {
  border-left: 4px solid var(--primary-color);
}

.arch-layer--provider {
  border-left: 4px solid var(--secondary-color);
}

.arch-layer--fluent {
  border-left: 4px solid var(--accent-color);
}

.arch-layer--chains {
  border-left: 4px solid #9945FF;
}

.layer-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.layer-components {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.component {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
  min-width: 120px;
}

.component:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.component--rust {
  border: 1px solid #CE422B;
}

.component--evm {
  border: 1px solid #627EEA;
}

.component--svm {
  border: 1px solid #9945FF;
}

.component--bridge {
  border: 1px solid var(--accent-color);
}

.component i {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.component span {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  text-align: center;
}

.arch-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.arch-svg {
  width: 100%;
  height: 100%;
}

.connection-line {
  stroke: var(--primary-color);
  stroke-width: 2;
  opacity: 0.4;
  stroke-dasharray: 5, 5;
  animation: connectionFlow 3s ease-in-out infinite;
}

.architecture__details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
}

.detail-section {
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.detail-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

.detail-title i {
  color: var(--primary-color);
}

.detail-description {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.detail-metrics {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: space-between;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
}

.metric-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.detail-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* ===== USER JOURNEY SECTION ===== */
.user-journey {
  padding: var(--spacing-4xl) 0;
  background: var(--background-section);
  position: relative;
}

.user-journey::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.journey__comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  margin-bottom: var(--spacing-4xl);
  position: relative;
  z-index: 1;
}

.journey__side {
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.journey__before {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
  border-left: 4px solid var(--error-color);
}

.journey__after {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
  border-left: 4px solid var(--accent-color);
}

.journey__title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xl);
}

.journey__title i {
  font-size: var(--font-size-lg);
}

.journey__before .journey__title {
  color: var(--error-color);
}

.journey__after .journey__title {
  color: var(--accent-color);
}

.journey__steps {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.journey__step {
  display: flex;
  gap: var(--spacing-md);
  align-items: flex-start;
}

.step__number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--background-card);
  border: 2px solid var(--gray-300);
  border-radius: 50%;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  flex-shrink: 0;
}

.journey__before .step__number {
  border-color: var(--error-color);
  color: var(--error-color);
}

.journey__after .step__number {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.step__content h4 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.step__content p {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: var(--spacing-sm);
}

.step__pain,
.step__benefit {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
}

.step__pain {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #FCA5A5;
}

.step__benefit {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  color: #6EE7B7;
}

.step__pain i,
.step__benefit i {
  font-size: var(--font-size-xs);
}

.journey__animation {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.animation__container {
  display: flex;
  align-items: center;
  gap: var(--spacing-2xl);
}

.user__avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--error-color), #F87171);
  border-radius: 50%;
  color: var(--white);
  font-size: var(--font-size-xl);
}

.transformation__arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  text-align: center;
}

.transformation__arrow span {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.transformation__arrow i {
  font-size: var(--font-size-2xl);
  animation: arrowPulse 2s ease-in-out infinite;
}

@keyframes arrowPulse {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(10px); }
}

.result__state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.result__state i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-color), #34D399);
  border-radius: 50%;
  color: var(--white);
  font-size: var(--font-size-xl);
}

.result__state span {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--accent-color);
}

/* ===== BENEFITS SECTION ===== */
.benefits {
  padding: var(--spacing-4xl) 0;
  background: var(--background-dark);
  position: relative;
}

.benefits::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.benefits__grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-4xl);
  margin-bottom: var(--spacing-4xl);
  position: relative;
  z-index: 1;
}

.benefit__category {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.category__title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.category__title i {
  color: var(--primary-color);
  background: none;
  -webkit-background-clip: unset;
  -webkit-text-fill-color: unset;
}

.benefit__items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.benefit__item {
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.benefit__item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  border-color: rgba(0, 212, 255, 0.3);
}

.benefit__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
}

.benefit__icon i {
  color: var(--background-dark);
  font-size: var(--font-size-lg);
}

.benefit__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.benefit__description {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.6;
}

.benefits__metrics {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  text-align: center;
}

.metrics__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metrics__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
}

.metric__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.metric__value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
}

.metric__label {
  font-size: var(--font-size-base);
  color: var(--text-light);
  text-align: center;
}

/* ===== TECHNICAL CREDIBILITY SECTION ===== */
.technical-credibility {
  padding: var(--spacing-4xl) 0;
  background: var(--background-section);
  position: relative;
}

.technical-credibility::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.credibility__content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xl);
}

.team__credentials,
.partnerships,
.achievements,
.community__stats {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.credentials__title,
.partnerships__title,
.achievements__title,
.stats__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.credentials__grid,
.partners__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.credential__item,
.partner__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.credential__item:hover,
.partner__item:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.credential__icon,
.partner__logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
}

.credential__icon i,
.partner__logo i {
  color: var(--background-dark);
  font-size: var(--font-size-xl);
}

.credential__title,
.partner__name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.credential__description,
.partner__description {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.6;
}

.achievements__timeline {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  position: relative;
}

.achievements__timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary-color), var(--secondary-color));
}

.timeline__item {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-start;
  position: relative;
}

.timeline__date {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  border-radius: 50%;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  text-align: center;
  flex-shrink: 0;
  z-index: 1;
}

.timeline__content {
  flex: 1;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.timeline__content h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.timeline__content p {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.6;
}

.community__stats .stats__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
}

.stat__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: var(--spacing-sm);
}

.stat__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
}

.stat__icon i {
  color: var(--background-dark);
  font-size: var(--font-size-lg);
}

.stat__value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
}

.stat__label {
  font-size: var(--font-size-base);
  color: var(--text-light);
}

/* ===== EARLY ACCESS SECTION ===== */
.early-access {
  padding: var(--spacing-4xl) 0;
  background: var(--background-dark);
  position: relative;
}

.early-access::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.early-access__content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4xl);
}

.access__tiers {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-2xl);
}

.tier {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.tier:hover {
  transform: translateY(-4px);
  border-color: rgba(0, 212, 255, 0.3);
}

.tier--alpha {
  border: 2px solid var(--primary-color);
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.05));
}

.tier--alpha::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.tier__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.tier__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tier__badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.tier__benefits {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.benefit {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.benefit i {
  color: var(--accent-color);
  font-size: var(--font-size-sm);
}

.benefit span {
  color: var(--text-light);
  font-size: var(--font-size-base);
}

.tier__cta {
  text-align: center;
}

.tier__btn {
  width: 100%;
  justify-content: center;
}

.signup__form {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
}

.form__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.access__form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form__row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.form__group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form__label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.form__input,
.form__select {
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: var(--font-size-base);
  transition: var(--transition);
}

.form__input:focus,
.form__select:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.08);
}

.form__input::placeholder {
  color: var(--text-muted);
}

.form__submit {
  width: 100%;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.access__stats {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  text-align: center;
}

.stats__header {
  margin-bottom: var(--spacing-xl);
}

.stats__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
}

.stat__number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
}

.stat__trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--accent-color);
}

.referral__program {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  text-align: center;
}

.referral__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.referral__description {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
}

.referral__tiers {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.referral__tier {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition);
}

.referral__tier:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.tier__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: var(--border-radius);
  flex-shrink: 0;
}

.tier__icon i {
  color: var(--background-dark);
  font-size: var(--font-size-lg);
}

.tier__info {
  text-align: left;
}

.tier__count {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.tier__reward {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

/* ===== UPDATED FOOTER STYLES ===== */
.footer__tech {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  flex-wrap: wrap;
}

.tech__badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.tech__badge i {
  color: var(--primary-color);
}

.footer__bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

/* ===== RESPONSIVE DESIGN FOR NEW SECTIONS ===== */
@media (max-width: 1200px) {
  .work-step,
  .work-step--reverse {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .work-step--reverse .step__visual,
  .work-step--reverse .step__content {
    order: unset;
  }

  .architecture__details {
    grid-template-columns: 1fr;
  }

  .journey__comparison {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
}

@media (max-width: 768px) {
  .hero__features {
    gap: var(--spacing-sm);
  }

  .feature-highlight {
    padding: var(--spacing-sm);
  }

  .hero__cta {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .universal-connection-demo {
    height: 400px;
  }

  .connection-flow {
    gap: var(--spacing-lg);
  }

  .blockchain-layer,
  .fluent-layer,
  .dapp-layer {
    gap: var(--spacing-sm);
  }

  .dapp-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
  }

  .step__diagram {
    max-width: 300px;
    padding: var(--spacing-md);
  }

  .layer-components {
    gap: var(--spacing-sm);
  }

  .component {
    min-width: 100px;
    padding: var(--spacing-sm);
  }

  .detail-metrics {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .journey__step {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .animation__container {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .transformation__arrow {
    transform: rotate(90deg);
  }

  .benefit__items {
    grid-template-columns: 1fr;
  }

  .metrics__grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .credentials__grid,
  .partners__grid {
    grid-template-columns: 1fr;
  }

  .achievements__timeline::before {
    left: 15px;
  }

  .timeline__date {
    width: 30px;
    height: 30px;
    font-size: var(--font-size-xs);
  }

  .timeline__item {
    gap: var(--spacing-md);
  }

  .community__stats .stats__grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .access__tiers {
    grid-template-columns: 1fr;
  }

  .form__row {
    grid-template-columns: 1fr;
  }

  .stats__grid {
    grid-template-columns: 1fr;
  }

  .referral__tiers {
    grid-template-columns: 1fr;
  }

  .referral__tier {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .tier__info {
    text-align: center;
  }

  .footer__bottom-content {
    flex-direction: column;
    text-align: center;
  }

  .tech__badge {
    font-size: var(--font-size-2xs);
    padding: 2px var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .hero__title {
    font-size: var(--font-size-3xl);
  }

  .hero__description {
    font-size: var(--font-size-base);
    max-width: 100%;
  }

  .universal-connection-demo {
    height: 300px;
    padding: var(--spacing-md);
  }

  .step__number {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-lg);
  }

  .step__title {
    font-size: var(--font-size-xl);
  }

  .component {
    min-width: 80px;
    padding: var(--spacing-xs);
  }

  .component span {
    font-size: var(--font-size-2xs);
  }

  .metrics__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .metric__value {
    font-size: var(--font-size-2xl);
  }

  .community__stats .stats__grid {
    grid-template-columns: 1fr;
  }

  .tier {
    padding: var(--spacing-lg);
  }

  .tier__header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }
}
