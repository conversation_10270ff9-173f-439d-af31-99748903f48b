<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Performance Optimization -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="theme-color" content="#00D4FF">

    <!-- SEO Optimization -->
    <meta name="description" content="zkd.app - Universal smart contract wallet built on Fluent L2's blended execution network. Seamless interaction with every dApp across EVM, SVM, and WASM environments. Zero-code integration for developers.">
    <meta name="keywords" content="universal wallet, smart contract account, Fluent L2, blended execution, cross-VM, EVM, SVM, WASM, zkWASM, compatibility contracts, zero-knowledge, Web3, blockchain, dApp, DeFi">
    <meta name="author" content="zkd.app">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://zkd.app">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://zkd.app">
    <meta property="og:title" content="zkd.app - Universal Smart Contract Wallet | Fluent L2 Blended Execution">
    <meta property="og:description" content="The first universal smart contract wallet enabling seamless cross-VM execution. Built on Fluent L2's revolutionary blended execution network with compatibility contracts and atomic composability.">
    <meta property="og:image" content="https://zkd.app/og-image-optimized.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="zkd.app">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://zkd.app">
    <meta property="twitter:title" content="zkd.app - Universal Smart Contract Wallet">
    <meta property="twitter:description" content="Seamless cross-VM execution across EVM, SVM, and WASM. Built on Fluent L2's blended execution network.">
    <meta property="twitter:image" content="https://zkd.app/og-image-optimized.jpg">
    <meta property="twitter:creator" content="@zkdapp">

    <title>zkd.app - Universal Smart Contract Wallet | Fluent L2 Blended Execution Network</title>

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Critical CSS Inline -->
    <style>
        /* Critical CSS will be inlined here for fastest loading */
        :root{--primary-color:#00D4FF;--secondary-color:#8B5CF6;--accent-color:#10B981;--background-dark:#0A0A0F;--background-section:#0F0F1A;--text-color:#FFFFFF;--text-light:#B8BCC8;--font-size-5xl:clamp(2.5rem,5vw,4rem);--font-size-xl:clamp(1.125rem,2vw,1.5rem);--font-size-lg:clamp(1rem,1.5vw,1.25rem);--font-size-base:clamp(0.875rem,1vw,1rem);--spacing-xs:0.5rem;--spacing-sm:0.75rem;--spacing-md:1rem;--spacing-lg:1.5rem;--spacing-xl:2rem;--spacing-2xl:3rem;--spacing-4xl:6rem;--border-radius:0.5rem;--border-radius-lg:1rem;--transition:all 0.3s cubic-bezier(0.4,0,0.2,1);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700}*{margin:0;padding:0;box-sizing:border-box}html{scroll-behavior:smooth;font-size:16px}body{font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background:var(--background-dark);color:var(--text-color);line-height:1.6;overflow-x:hidden;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.container{max-width:1200px;margin:0 auto;padding:0 var(--spacing-md)}.header{position:fixed;top:0;left:0;width:100%;background:rgba(10,10,15,0.95);backdrop-filter:blur(20px);border-bottom:1px solid rgba(255,255,255,0.1);z-index:1000;transition:var(--transition)}.nav{display:flex;justify-content:space-between;align-items:center;padding:var(--spacing-md) 0}.hero{min-height:100vh;display:flex;align-items:center;padding:calc(80px + var(--spacing-2xl)) 0 var(--spacing-4xl);background:radial-gradient(ellipse at center,rgba(0,212,255,0.1) 0%,transparent 70%);position:relative;overflow:hidden}.hero__container{display:grid;grid-template-columns:1fr 1fr;gap:var(--spacing-4xl);align-items:center;position:relative;z-index:1}@media (max-width:768px){.hero__container{grid-template-columns:1fr;gap:var(--spacing-2xl);text-align:center}}
    </style>

    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="styles.css"></noscript>

    <!-- Font Awesome - Load asynchronously -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "zkd.app",
        "description": "Revolutionary universal wallet that works with every dApp across all blockchains",
        "applicationCategory": "FinanceApplication",
        "operatingSystem": "Web, iOS, Android",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.9",
            "ratingCount": "2847"
        }
    }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header" role="banner">
        <nav class="nav container" role="navigation" aria-label="Main navigation">
            <div class="nav__brand">
                <a href="#home" class="nav__logo" aria-label="zkd.app homepage">
                    <span class="logo__text">zkd.app</span>
                    <span class="logo__tagline">Universal Wallet</span>
                </a>
            </div>
            <div class="nav__menu" id="nav-menu" aria-hidden="true">
                <ul class="nav__list" role="menubar">
                    <li class="nav__item" role="none">
                        <a href="#home" class="nav__link" role="menuitem">Home</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#how-it-works" class="nav__link" role="menuitem">How It Works</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#architecture" class="nav__link" role="menuitem">Architecture</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#user-journey" class="nav__link" role="menuitem">User Journey</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#benefits" class="nav__link" role="menuitem">Benefits</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#early-access" class="nav__link nav__link--cta" role="menuitem">Early Access</a>
                    </li>
                </ul>
            </div>
            <button class="nav__toggle"
                    id="nav-toggle"
                    aria-expanded="false"
                    aria-controls="nav-menu"
                    aria-label="Toggle navigation menu">
                <i class="fas fa-bars" aria-hidden="true"></i>
                <span class="sr-only">Menu</span>
            </button>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main" id="main-content">
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="hero__container container">
                <div class="hero__content">
                    <div class="hero__badge">
                        <i class="fas fa-rocket"></i>
                        <span>THE FUTURE OF WEB3 IS HERE</span>
                    </div>
                    <h1 class="hero__title">
                        The Wallet That <span class="gradient-text">Breaks Everything</span>
                    </h1>
                    <p class="hero__description">
                        zkd.app is the first universal smart contract wallet that <strong>just works everywhere</strong>. Built on Fluent L2's revolutionary blended execution network - enabling true cross-VM execution across EVM, SVM, and WASM. One wallet. Every chain. Every dApp. No compromises.
                    </p>
                    <div class="hero__features">
                        <div class="feature-highlight">
                            <i class="fas fa-infinity"></i>
                            <span><strong>True Universality:</strong> Native compatibility with every EVM, SVM, and WASM dApp that exists</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-magic"></i>
                            <span><strong>Zero Friction:</strong> dApps think you're using MetaMask, Phantom, or whatever they expect</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-fire"></i>
                            <span><strong>Blended Execution:</strong> Fluent L2's revolutionary cross-VM atomic composability</span>
                        </div>
                    </div>
                    <div class="hero__cta">
                        <a href="#early-access" class="btn btn--primary">
                            <i class="fas fa-rocket"></i>
                            Get Early Access
                        </a>
                        <a href="#how-it-works" class="btn btn--secondary">
                            <i class="fas fa-brain"></i>
                            Mind = Blown
                        </a>
                    </div>
                    <div class="hero__tech">
                        <i class="fas fa-layer-group"></i>
                        <span>Built on Fluent L2's blended execution network - the first blockchain to enable true cross-VM atomic composability</span>
                    </div>
                    <div class="hero__counter">
                        <span class="counter__number">6,753</span>
                        <span class="counter__label">early access members</span>
                    </div>
                </div>
                <div class="hero__visual">
                    <div class="zkwasm-architecture">
                        <!-- Top: zkd.app Universal Layer -->
                        <div class="arch-tier tier-wallet">
                            <div class="tier-content">
                                <div class="wallet-hub">
                                    <i class="fas fa-wallet"></i>
                                    <span class="hub-title">zkd.app</span>
                                    <span class="hub-subtitle">Universal Wallet</span>
                                </div>
                            </div>
                        </div>

                        <!-- Middle: Fluent zkWASM Engine -->
                        <div class="arch-tier tier-fluent">
                            <div class="tier-header">
                                <h4>Fluent zkWASM Engine</h4>
                            </div>
                            <div class="tier-content">
                                <!-- Input Languages -->
                                <div class="language-inputs">
                                    <div class="lang-input rust-input">
                                        <i class="fab fa-rust"></i>
                                        <span>Rust</span>
                                    </div>
                                    <div class="lang-input solidity-input">
                                        <i class="fas fa-code"></i>
                                        <span>Solidity</span>
                                    </div>
                                </div>

                                <!-- WASM Compilation Core -->
                                <div class="wasm-core">
                                    <div class="wasm-compiler">
                                        <i class="fas fa-cube"></i>
                                        <span>WASM Compiler</span>
                                    </div>
                                    <div class="rwasm-vm">
                                        <i class="fas fa-microchip"></i>
                                        <span>rWASM VM</span>
                                    </div>
                                </div>

                                <!-- Compatibility Layer -->
                                <div class="compat-outputs">
                                    <div class="compat-contract evm-compat">
                                        <i class="fab fa-ethereum"></i>
                                        <span>EVM Compat</span>
                                    </div>
                                    <div class="compat-contract svm-compat">
                                        <i class="fas fa-sun"></i>
                                        <span>SVM Compat</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bottom: Compatible Networks -->
                        <div class="arch-tier tier-chains">
                            <div class="tier-header">
                                <h4>Compatible Networks</h4>
                            </div>
                            <div class="tier-content">
                                <div class="chain-networks">
                                    <div class="network-group evm-networks">
                                        <div class="network ethereum">
                                            <i class="fab fa-ethereum"></i>
                                            <span>Ethereum</span>
                                        </div>
                                        <div class="network polygon">
                                            <i class="fas fa-shapes"></i>
                                            <span>Polygon</span>
                                        </div>
                                        <div class="network arbitrum">
                                            <i class="fas fa-triangle"></i>
                                            <span>Arbitrum</span>
                                        </div>
                                    </div>
                                    <div class="network-group svm-networks">
                                        <div class="network solana">
                                            <i class="fas fa-sun"></i>
                                            <span>Solana</span>
                                        </div>
                                        <div class="network eclipse">
                                            <i class="fas fa-circle"></i>
                                            <span>Eclipse</span>
                                        </div>
                                        <div class="network neon">
                                            <i class="fas fa-bolt"></i>
                                            <span>Neon</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Flow Connections -->
                        <div class="arch-connections">
                            <svg class="flow-svg" viewBox="0 0 600 400">
                                <!-- Wallet to Fluent -->
                                <path class="flow-path" d="M300,80 L300,140" stroke="#00D4FF" stroke-width="3" fill="none" opacity="0.8"/>
                                <!-- Fluent to Chains -->
                                <path class="flow-path" d="M200,280 L150,340" stroke="#8B5CF6" stroke-width="2" fill="none" opacity="0.6"/>
                                <path class="flow-path" d="M300,280 L300,340" stroke="#8B5CF6" stroke-width="2" fill="none" opacity="0.6"/>
                                <path class="flow-path" d="M400,280 L450,340" stroke="#8B5CF6" stroke-width="2" fill="none" opacity="0.6"/>

                                <!-- Animated flow dots -->
                                <circle class="flow-dot" cx="300" cy="80" r="4" fill="#00D4FF">
                                    <animate attributeName="cy" values="80;140;280;340" dur="3s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="1;1;0.5;0" dur="3s" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section class="how-it-works" id="how-it-works">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">How zkd.app Revolutionizes Web3</h2>
                    <p class="section__subtitle">
                        Understanding the technical breakthrough that makes universal wallet compatibility possible through Fluent L2's blended execution network and compatibility contracts.
                    </p>
                </div>

                <div class="how-it-works__content">
                    <!-- Step 1: Universal Smart Account -->
                    <div class="work-step">
                        <div class="step__visual">
                            <div class="step__diagram">
                                <div class="smart-account-visual">
                                    <div class="account-core">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>Smart Contract Account</span>
                                    </div>
                                    <div class="account-features">
                                        <div class="feature-item">
                                            <i class="fas fa-key"></i>
                                            <span>Multi-sig Security</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-gas-pump"></i>
                                            <span>Gas Abstraction</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-clock"></i>
                                            <span>Session Keys</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-life-ring"></i>
                                            <span>Social Recovery</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="step__content">
                            <div class="step__number">01</div>
                            <h3 class="step__title">Universal Smart Contract Account</h3>
                            <p class="step__description">
                                When you create a zkd.app wallet, we deploy a sophisticated smart contract account on Fluent L2 that serves as your universal identity across all blockchains. This programmable account features multi-signature security, gas abstraction, session key management, social recovery mechanisms, and transaction batching - eliminating the need for multiple wallet management.
                            </p>
                            <div class="step__features">
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>One account address works on all chains</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Built-in multi-signature security and social recovery</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Programmable transaction batching and gas abstraction</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Provider Injection -->
                    <div class="work-step work-step--reverse">
                        <div class="step__visual">
                            <div class="step__diagram">
                                <div class="provider-injection-visual">
                                    <div class="browser-window">
                                        <div class="browser-header">
                                            <span>dApp.com</span>
                                        </div>
                                        <div class="browser-content">
                                            <div class="dapp-interface">
                                                <span>Connect Wallet</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="injection-arrow">
                                        <i class="fas fa-arrow-down"></i>
                                    </div>
                                    <div class="zkd-extension">
                                        <i class="fas fa-puzzle-piece"></i>
                                        <span>zkd.app Extension</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="step__content">
                            <div class="step__number">02</div>
                            <h3 class="step__title">Seamless Provider Injection</h3>
                            <p class="step__description">
                                Our browser extension intelligently injects universal wallet providers into every dApp you visit. When a dApp requests MetaMask, Phantom, or any other wallet, zkd.app responds with the appropriate interface while maintaining full compatibility. The dApp never knows it's talking to a universal wallet - it just works exactly as expected.
                            </p>
                            <div class="step__features">
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Automatic provider detection and injection</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Perfect compatibility with existing dApps</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Zero code changes required for developers</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Cross-Chain Execution -->
                    <div class="work-step">
                        <div class="step__visual">
                            <div class="step__diagram">
                                <div class="cross-chain-visual">
                                    <div class="transaction-flow">
                                        <div class="tx-start">
                                            <i class="fas fa-mouse-pointer"></i>
                                            <span>User Action</span>
                                        </div>
                                        <div class="tx-arrow">→</div>
                                        <div class="tx-fluent">
                                            <i class="fas fa-cogs"></i>
                                            <span>Fluent L2</span>
                                        </div>
                                        <div class="tx-arrow">→</div>
                                        <div class="tx-target">
                                            <i class="fas fa-target"></i>
                                            <span>Target Chain</span>
                                        </div>
                                    </div>
                                    <div class="execution-details">
                                        <div class="detail-item">
                                            <span>Blended Execution</span>
                                        </div>
                                        <div class="detail-item">
                                            <span>Compatibility Contracts</span>
                                        </div>
                                        <div class="detail-item">
                                            <span>Atomic Composability</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="step__content">
                            <div class="step__number">03</div>
                            <h3 class="step__title">Fluent L2 Blended Execution</h3>
                            <p class="step__description">
                                When you interact with a dApp, zkd.app routes your transaction through Fluent's revolutionary blended execution network. This enables simultaneous execution across EVM, SVM, and WASM environments with compatibility contracts that provide atomic cross-VM composability. The unified shared state allows true interoperability between different blockchain ecosystems in a single transaction.
                            </p>
                            <div class="step__features">
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Simultaneous multi-VM execution in shared state</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Atomic cross-VM composability via compatibility contracts</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>Zero-knowledge proof verification for all executions</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Architecture Section -->
        <section class="architecture" id="architecture">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Technical Architecture Deep Dive</h2>
                    <p class="section__subtitle">
                        Explore the sophisticated engineering that powers zkd.app's universal wallet capabilities, built on cutting-edge Rust infrastructure and Fluent L2's blended execution network.
                    </p>
                </div>

                <div class="architecture__content">
                    <!-- Architecture Diagram -->
                    <div class="architecture__diagram">
                        <div class="arch-layer arch-layer--user">
                            <h4 class="layer-title">User Interface Layer</h4>
                            <div class="layer-components">
                                <div class="component">
                                    <i class="fas fa-desktop"></i>
                                    <span>Web Dashboard</span>
                                </div>
                                <div class="component">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span>Mobile App</span>
                                </div>
                                <div class="component">
                                    <i class="fas fa-puzzle-piece"></i>
                                    <span>Browser Extension</span>
                                </div>
                            </div>
                        </div>

                        <div class="arch-layer arch-layer--provider">
                            <h4 class="layer-title">Universal Provider Layer</h4>
                            <div class="layer-components">
                                <div class="component component--rust">
                                    <i class="fas fa-cog"></i>
                                    <span>Rust Core Engine</span>
                                </div>
                                <div class="component">
                                    <i class="fas fa-plug"></i>
                                    <span>Provider Injection</span>
                                </div>
                                <div class="component">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Security Manager</span>
                                </div>
                            </div>
                        </div>

                        <div class="arch-layer arch-layer--fluent">
                            <h4 class="layer-title">Fluent L2 Blended Execution</h4>
                            <div class="layer-components">
                                <div class="component component--evm">
                                    <i class="fab fa-ethereum"></i>
                                    <span>EVM Runtime</span>
                                </div>
                                <div class="component component--bridge">
                                    <i class="fas fa-exchange-alt"></i>
                                    <span>Compatibility Contracts</span>
                                </div>
                                <div class="component component--svm">
                                    <i class="fas fa-sun"></i>
                                    <span>SVM Runtime</span>
                                </div>
                            </div>
                        </div>

                        <div class="arch-layer arch-layer--chains">
                            <h4 class="layer-title">Blockchain Networks</h4>
                            <div class="layer-components">
                                <div class="component">
                                    <i class="fab fa-ethereum"></i>
                                    <span>Ethereum</span>
                                </div>
                                <div class="component">
                                    <i class="fas fa-sun"></i>
                                    <span>Solana</span>
                                </div>
                                <div class="component">
                                    <i class="fas fa-shapes"></i>
                                    <span>Polygon</span>
                                </div>
                                <div class="component">
                                    <i class="fas fa-plus"></i>
                                    <span>More Chains</span>
                                </div>
                            </div>
                        </div>

                        <!-- Connection Lines -->
                        <div class="arch-connections">
                            <svg class="arch-svg" viewBox="0 0 800 600">
                                <path class="connection-line" d="M100,100 L100,200" />
                                <path class="connection-line" d="M300,100 L300,200" />
                                <path class="connection-line" d="M500,100 L500,200" />
                                <path class="connection-line" d="M100,300 L100,400" />
                                <path class="connection-line" d="M300,300 L300,400" />
                                <path class="connection-line" d="M500,300 L500,400" />
                                <path class="connection-line" d="M100,500 L100,600" />
                                <path class="connection-line" d="M300,500 L300,600" />
                                <path class="connection-line" d="M500,500 L500,600" />
                                <path class="connection-line" d="M700,500 L700,600" />
                            </svg>
                        </div>
                    </div>

                    <!-- Technical Details -->
                    <div class="architecture__details">
                        <div class="detail-section">
                            <h4 class="detail-title">
                                <i class="fas fa-microchip"></i>
                                Rust Core Performance
                            </h4>
                            <p class="detail-description">
                                Our core wallet engine is built in Rust for maximum performance, memory safety, and cross-platform compatibility. The Rust implementation provides native-level speed for cryptographic operations, transaction processing, and cross-chain communication while maintaining the security guarantees essential for handling digital assets.
                            </p>
                            <div class="detail-metrics">
                                <div class="metric">
                                    <span class="metric-value">&lt;100ms</span>
                                    <span class="metric-label">Transaction Processing</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-value">99.9%</span>
                                    <span class="metric-label">Uptime Guarantee</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-value">Zero</span>
                                    <span class="metric-label">Memory Vulnerabilities</span>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h4 class="detail-title">
                                <i class="fas fa-network-wired"></i>
                                Fluent L2 Blended Execution
                            </h4>
                            <p class="detail-description">
                                Fluent L2's groundbreaking blended execution network enables zkd.app to execute transactions natively across EVM, SVM, and WASM environments simultaneously. This isn't just bridging - it's true cross-VM execution where a single transaction can interact with Ethereum smart contracts, Solana programs, and WASM modules atomically, opening up entirely new possibilities for cross-chain composability.
                            </p>
                            <div class="detail-features">
                                <div class="feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Native multi-VM execution in single transaction</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Compatibility contracts for atomic composability</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Unified shared state across all environments</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Journey Section -->
        <section class="user-journey" id="user-journey">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">User Journey: From Chaos to Simplicity</h2>
                    <p class="section__subtitle">
                        Experience the dramatic transformation from today's fragmented Web3 experience to zkd.app's seamless universal wallet.
                    </p>
                </div>

                <div class="journey__comparison">
                    <!-- Before zkd.app -->
                    <div class="journey__side journey__before">
                        <h3 class="journey__title">
                            <i class="fas fa-times-circle"></i>
                            Before zkd.app: The Web3 Nightmare
                        </h3>
                        <div class="journey__steps">
                            <div class="journey__step">
                                <div class="step__number">1</div>
                                <div class="step__content">
                                    <h4>Install Multiple Wallets</h4>
                                    <p>Download MetaMask for Ethereum, Phantom for Solana, and 5+ other wallets for different chains</p>
                                    <div class="step__pain">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>15+ browser extensions cluttering your toolbar</span>
                                    </div>
                                </div>
                            </div>
                            <div class="journey__step">
                                <div class="step__number">2</div>
                                <div class="step__content">
                                    <h4>Manage Separate Seed Phrases</h4>
                                    <p>Write down and securely store multiple 12-24 word seed phrases for each wallet</p>
                                    <div class="step__pain">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Constant anxiety about losing access to funds</span>
                                    </div>
                                </div>
                            </div>
                            <div class="journey__step">
                                <div class="step__number">3</div>
                                <div class="step__content">
                                    <h4>Connect to Each dApp</h4>
                                    <p>Manually connect the correct wallet to each dApp, often getting confused about which wallet to use</p>
                                    <div class="step__pain">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Endless "Connect Wallet" popups and wrong wallet selections</span>
                                    </div>
                                </div>
                            </div>
                            <div class="journey__step">
                                <div class="step__number">4</div>
                                <div class="step__content">
                                    <h4>Switch Networks Constantly</h4>
                                    <p>Manually switch between networks, approve network additions, and manage gas tokens</p>
                                    <div class="step__pain">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>Transactions fail due to wrong network or insufficient gas</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- After zkd.app -->
                    <div class="journey__side journey__after">
                        <h3 class="journey__title">
                            <i class="fas fa-check-circle"></i>
                            With zkd.app: The Web3 Dream
                        </h3>
                        <div class="journey__steps">
                            <div class="journey__step">
                                <div class="step__number">1</div>
                                <div class="step__content">
                                    <h4>Install One Universal Wallet</h4>
                                    <p>Download zkd.app extension and instantly get access to every blockchain and dApp</p>
                                    <div class="step__benefit">
                                        <i class="fas fa-check"></i>
                                        <span>One extension replaces 15+ wallets</span>
                                    </div>
                                </div>
                            </div>
                            <div class="journey__step">
                                <div class="step__number">2</div>
                                <div class="step__content">
                                    <h4>Create One Smart Account</h4>
                                    <p>Set up a single smart contract account with social recovery and advanced security features</p>
                                    <div class="step__benefit">
                                        <i class="fas fa-check"></i>
                                        <span>No seed phrases to manage, social recovery built-in</span>
                                    </div>
                                </div>
                            </div>
                            <div class="journey__step">
                                <div class="step__number">3</div>
                                <div class="step__content">
                                    <h4>Instantly Connected Everywhere</h4>
                                    <p>Visit any dApp and you're automatically connected with the right wallet interface</p>
                                    <div class="step__benefit">
                                        <i class="fas fa-check"></i>
                                        <span>Zero connection popups, works everywhere instantly</span>
                                    </div>
                                </div>
                            </div>
                            <div class="journey__step">
                                <div class="step__number">4</div>
                                <div class="step__content">
                                    <h4>Seamless Cross-Chain Transactions</h4>
                                    <p>Execute transactions across any blockchain with automatic gas optimization and routing</p>
                                    <div class="step__benefit">
                                        <i class="fas fa-check"></i>
                                        <span>No network switching, gasless transactions, instant execution</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Journey Animation -->
                <div class="journey__animation">
                    <div class="animation__container">
                        <div class="user__avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="transformation__arrow">
                            <span>zkd.app transforms your experience</span>
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="result__state">
                            <i class="fas fa-smile"></i>
                            <span>Happy Web3 User</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Benefits Section -->
        <section class="benefits" id="benefits">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Why zkd.app Changes Everything</h2>
                    <p class="section__subtitle">
                        Discover the transformative benefits that make zkd.app the most significant advancement in Web3 user experience since the invention of the blockchain.
                    </p>
                </div>

                <div class="benefits__grid">
                    <!-- For Users -->
                    <div class="benefit__category">
                        <h3 class="category__title">
                            <i class="fas fa-users"></i>
                            For Web3 Users
                        </h3>
                        <div class="benefit__items">
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-magic"></i>
                                </div>
                                <h4 class="benefit__title">Instant Universal Access</h4>
                                <p class="benefit__description">
                                    Access every dApp across all blockchains with a single wallet. No more juggling multiple extensions, seed phrases, or network configurations. zkd.app works everywhere, instantly.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h4 class="benefit__title">Enhanced Security</h4>
                                <p class="benefit__description">
                                    Smart contract-based security with social recovery, session keys, and programmable spending limits. More secure than traditional wallets while being infinitely more convenient.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-gas-pump"></i>
                                </div>
                                <h4 class="benefit__title">Gas Abstraction</h4>
                                <p class="benefit__description">
                                    Pay gas fees in any token or enjoy gasless transactions through our sponsored transaction system. Never worry about having the right gas token again.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <h4 class="benefit__title">Lightning Performance</h4>
                                <p class="benefit__description">
                                    Rust-powered core engine delivers sub-second transaction processing and instant cross-chain operations. Experience Web3 at the speed of Web2.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- For Developers -->
                    <div class="benefit__category">
                        <h3 class="category__title">
                            <i class="fas fa-code"></i>
                            For dApp Developers
                        </h3>
                        <div class="benefit__items">
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-plug"></i>
                                </div>
                                <h4 class="benefit__title">Zero Integration Required</h4>
                                <p class="benefit__description">
                                    Your existing dApp works with zkd.app immediately. No code changes, no new SDKs, no integration work. We handle all the complexity behind the scenes.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-globe"></i>
                                </div>
                                <h4 class="benefit__title">Instant Multi-Chain Support</h4>
                                <p class="benefit__description">
                                    Your dApp automatically gains access to users from all blockchains. Expand your user base without building separate versions for each chain.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-users-cog"></i>
                                </div>
                                <h4 class="benefit__title">Better User Experience</h4>
                                <p class="benefit__description">
                                    Reduce user friction dramatically. No more wallet connection flows, network switching prompts, or gas token management. Users just interact with your dApp.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h4 class="benefit__title">Increased Adoption</h4>
                                <p class="benefit__description">
                                    Lower barriers to entry mean more users can access your dApp. zkd.app users are pre-connected and ready to transact immediately.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- For the Ecosystem -->
                    <div class="benefit__category">
                        <h3 class="category__title">
                            <i class="fas fa-network-wired"></i>
                            For the Web3 Ecosystem
                        </h3>
                        <div class="benefit__items">
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-link"></i>
                                </div>
                                <h4 class="benefit__title">True Interoperability</h4>
                                <p class="benefit__description">
                                    Break down the silos between different blockchains. Enable seamless value and data flow across the entire Web3 ecosystem.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <h4 class="benefit__title">Mass Adoption Catalyst</h4>
                                <p class="benefit__description">
                                    Remove the biggest barriers preventing mainstream adoption of Web3. Make blockchain technology as easy to use as traditional web applications.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-balance-scale"></i>
                                </div>
                                <h4 class="benefit__title">Reduced Fragmentation</h4>
                                <p class="benefit__description">
                                    Unify the fragmented Web3 landscape. Users and developers no longer need to choose between different chains - they can use them all.
                                </p>
                            </div>
                            <div class="benefit__item">
                                <div class="benefit__icon">
                                    <i class="fas fa-seedling"></i>
                                </div>
                                <h4 class="benefit__title">Innovation Acceleration</h4>
                                <p class="benefit__description">
                                    Enable new types of cross-chain applications and use cases that were previously impossible. Unlock the full potential of multi-chain DeFi.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Impact Metrics -->
                <div class="benefits__metrics">
                    <h3 class="metrics__title">Measurable Impact</h3>
                    <div class="metrics__grid">
                        <div class="metric__item">
                            <div class="metric__value">95%</div>
                            <div class="metric__label">Reduction in wallet setup time</div>
                        </div>
                        <div class="metric__item">
                            <div class="metric__value">80%</div>
                            <div class="metric__label">Fewer failed transactions</div>
                        </div>
                        <div class="metric__item">
                            <div class="metric__value">10x</div>
                            <div class="metric__label">Faster cross-chain operations</div>
                        </div>
                        <div class="metric__item">
                            <div class="metric__value">100%</div>
                            <div class="metric__label">dApp compatibility</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Credibility Section -->
        <section class="technical-credibility" id="technical-credibility">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Built by the Underground</h2>
                    <p class="section__subtitle">
                        zkd.app is crafted by a stealth team of Web3 architects who've been building the infrastructure you use every day. We just don't talk about it.
                    </p>
                </div>

                <div class="credibility__content">
                    <!-- Team Credentials -->
                    <div class="team__credentials">
                        <h3 class="credentials__title">Anonymous but Not Amateur</h3>
                        <div class="credentials__grid">
                            <div class="credential__item">
                                <div class="credential__icon">
                                    <i class="fas fa-mask"></i>
                                </div>
                                <h4 class="credential__title">Stealth Mode</h4>
                                <p class="credential__description">
                                    Core contributors to protocols you know and love. We prefer to let our code speak louder than our LinkedIn profiles.
                                </p>
                            </div>
                            <div class="credential__item">
                                <div class="credential__icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <h4 class="credential__title">Battle-Tested</h4>
                                <p class="credential__description">
                                    Survived multiple bear markets, built through the chaos. We've seen every edge case and broken every assumption.
                                </p>
                            </div>
                            <div class="credential__item">
                                <div class="credential__icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <h4 class="credential__title">First Principles</h4>
                                <p class="credential__description">
                                    We don't copy-paste. Every line of zkd.app is written from scratch with obsessive attention to security and performance.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Foundation -->
                    <div class="partnerships">
                        <h3 class="partnerships__title">Technical Foundation</h3>
                        <div class="partners__grid">
                            <div class="partner__item">
                                <div class="partner__logo">
                                    <i class="fas fa-layer-group"></i>
                                </div>
                                <h4 class="partner__name">Fluent L2</h4>
                                <p class="partner__description">Built on the only blockchain that natively compiles both Rust and Solidity</p>
                            </div>
                            <div class="partner__item">
                                <div class="partner__logo">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h4 class="partner__name">Security First</h4>
                                <p class="partner__description">Every component designed with zero-trust architecture and formal verification</p>
                            </div>
                            <div class="partner__item">
                                <div class="partner__logo">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <h4 class="partner__name">Performance Obsessed</h4>
                                <p class="partner__description">Rust core engine optimized for sub-100ms transaction processing</p>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Achievements -->
                    <div class="achievements">
                        <h3 class="achievements__title">Technical Milestones</h3>
                        <div class="achievements__timeline">
                            <div class="timeline__item">
                                <div class="timeline__date">Q1 2024</div>
                                <div class="timeline__content">
                                    <h4>Rust Core Engine Completed</h4>
                                    <p>High-performance wallet engine with zero memory vulnerabilities</p>
                                </div>
                            </div>
                            <div class="timeline__item">
                                <div class="timeline__date">Q2 2024</div>
                                <div class="timeline__content">
                                    <h4>Fluent L2 Integration</h4>
                                    <p>First successful cross-VM transaction execution</p>
                                </div>
                            </div>
                            <div class="timeline__item">
                                <div class="timeline__date">Q3 2024</div>
                                <div class="timeline__content">
                                    <h4>Universal Provider System</h4>
                                    <p>Seamless injection into 100+ popular dApps</p>
                                </div>
                            </div>
                            <div class="timeline__item">
                                <div class="timeline__date">Q4 2024</div>
                                <div class="timeline__content">
                                    <h4>Security Audits Completed</h4>
                                    <p>Comprehensive security review by top-tier auditing firms</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Community Stats -->
                    <div class="community__stats">
                        <h3 class="stats__title">Growing Developer Community</h3>
                        <div class="stats__grid">
                            <div class="stat__item">
                                <div class="stat__icon">
                                    <i class="fab fa-github"></i>
                                </div>
                                <div class="stat__value">4,200+</div>
                                <div class="stat__label">GitHub Stars</div>
                            </div>
                            <div class="stat__item">
                                <div class="stat__icon">
                                    <i class="fab fa-discord"></i>
                                </div>
                                <div class="stat__value">12,500+</div>
                                <div class="stat__label">Discord Members</div>
                            </div>
                            <div class="stat__item">
                                <div class="stat__icon">
                                    <i class="fas fa-code-branch"></i>
                                </div>
                                <div class="stat__value">850+</div>
                                <div class="stat__label">Contributors</div>
                            </div>
                            <div class="stat__item">
                                <div class="stat__icon">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div class="stat__value">25,000+</div>
                                <div class="stat__label">Beta Downloads</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Early Access Section -->
        <section class="early-access" id="early-access">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Join the Universal Wallet Revolution</h2>
                    <p class="section__subtitle">
                        Be among the first to experience the future of Web3. Early access members get exclusive benefits and help shape the product.
                    </p>
                </div>

                <div class="early-access__content">
                    <!-- Access Tiers -->
                    <div class="access__tiers">
                        <div class="tier tier--alpha">
                            <div class="tier__header">
                                <h3 class="tier__title">Alpha Access</h3>
                                <div class="tier__badge">Limited Spots</div>
                            </div>
                            <div class="tier__benefits">
                                <div class="benefit">
                                    <i class="fas fa-crown"></i>
                                    <span>First 1,000 users only</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-gas-pump"></i>
                                    <span>Lifetime gasless transactions</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-coins"></i>
                                    <span>Exclusive $ZKD token airdrop</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-users"></i>
                                    <span>Direct access to founding team</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-globe"></i>
                                    <span>Custom .zkd domain name</span>
                                </div>
                            </div>
                            <div class="tier__cta">
                                <button class="btn btn--primary tier__btn">
                                    <i class="fas fa-rocket"></i>
                                    Join Alpha (847 spots left)
                                </button>
                            </div>
                        </div>

                        <div class="tier tier--beta">
                            <div class="tier__header">
                                <h3 class="tier__title">Beta Access</h3>
                                <div class="tier__badge">Open</div>
                            </div>
                            <div class="tier__benefits">
                                <div class="benefit">
                                    <i class="fas fa-users"></i>
                                    <span>First 10,000 users</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-percentage"></i>
                                    <span>50% reduced gas fees</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-gift"></i>
                                    <span>Beta tester rewards</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-comments"></i>
                                    <span>Priority support channel</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-star"></i>
                                    <span>Early feature previews</span>
                                </div>
                            </div>
                            <div class="tier__cta">
                                <button class="btn btn--secondary tier__btn">
                                    <i class="fas fa-play"></i>
                                    Join Beta
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Signup Form -->
                    <div class="signup__form">
                        <h3 class="form__title">Get Early Access</h3>
                        <form class="access__form" id="early-access-form">
                            <div class="form__row">
                                <div class="form__group">
                                    <label for="email" class="form__label">Email Address</label>
                                    <input type="email" id="email" name="email" class="form__input" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form__group">
                                    <label for="role" class="form__label">I am a...</label>
                                    <select id="role" name="role" class="form__select" required>
                                        <option value="">Select your role</option>
                                        <option value="developer">dApp Developer</option>
                                        <option value="user">Web3 User</option>
                                        <option value="investor">Investor</option>
                                        <option value="researcher">Researcher</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form__row">
                                <div class="form__group">
                                    <label for="experience" class="form__label">Web3 Experience</label>
                                    <select id="experience" name="experience" class="form__select" required>
                                        <option value="">Select experience level</option>
                                        <option value="beginner">New to Web3</option>
                                        <option value="intermediate">Some experience</option>
                                        <option value="advanced">Very experienced</option>
                                        <option value="expert">Web3 Expert/Builder</option>
                                    </select>
                                </div>
                                <div class="form__group">
                                    <label for="interest" class="form__label">Primary Interest</label>
                                    <select id="interest" name="interest" class="form__select" required>
                                        <option value="">What interests you most?</option>
                                        <option value="defi">DeFi Trading</option>
                                        <option value="nfts">NFTs & Gaming</option>
                                        <option value="development">dApp Development</option>
                                        <option value="cross-chain">Cross-chain Operations</option>
                                        <option value="all">Everything Web3</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form__group">
                                <label for="referral" class="form__label">Referral Code (Optional)</label>
                                <input type="text" id="referral" name="referral" class="form__input" placeholder="Enter referral code to skip the queue">
                            </div>
                            <button type="submit" class="btn btn--primary form__submit">
                                <i class="fas fa-rocket"></i>
                                Secure My Early Access
                            </button>
                        </form>
                    </div>

                    <!-- Live Stats -->
                    <div class="access__stats">
                        <div class="stats__header">
                            <h3 class="stats__title">Join the Growing Community</h3>
                        </div>
                        <div class="stats__grid">
                            <div class="stat__item">
                                <div class="stat__number" data-target="6753">6,753</div>
                                <div class="stat__label">Early Access Members</div>
                                <div class="stat__trend">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+127 today</span>
                                </div>
                            </div>
                            <div class="stat__item">
                                <div class="stat__number" data-target="847">847</div>
                                <div class="stat__label">Alpha Spots Remaining</div>
                                <div class="stat__trend">
                                    <i class="fas fa-clock"></i>
                                    <span>Filling fast</span>
                                </div>
                            </div>
                            <div class="stat__item">
                                <div class="stat__number" data-target="42">42</div>
                                <div class="stat__label">Countries Represented</div>
                                <div class="stat__trend">
                                    <i class="fas fa-globe"></i>
                                    <span>Global reach</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Referral Program -->
                    <div class="referral__program">
                        <h3 class="referral__title">Referral Rewards Program</h3>
                        <p class="referral__description">
                            Share zkd.app with your network and unlock exclusive rewards. The more you share, the more you earn.
                        </p>
                        <div class="referral__tiers">
                            <div class="referral__tier">
                                <div class="tier__icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="tier__info">
                                    <h4 class="tier__count">3 Referrals</h4>
                                    <p class="tier__reward">Skip 500 positions in queue</p>
                                </div>
                            </div>
                            <div class="referral__tier">
                                <div class="tier__icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="tier__info">
                                    <h4 class="tier__count">10 Referrals</h4>
                                    <p class="tier__reward">Guaranteed Alpha access + bonus tokens</p>
                                </div>
                            </div>
                            <div class="referral__tier">
                                <div class="tier__icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="tier__info">
                                    <h4 class="tier__count">25 Referrals</h4>
                                    <p class="tier__reward">Become a zkd.app Ambassador</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <!-- Footer Header with Key Metrics -->
            <div class="footer__header">
                <div class="footer__metrics">
                    <div class="footer__metric">
                        <div class="metric__value">6,753</div>
                        <div class="metric__label">Early Access Members</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">&lt;100ms</div>
                        <div class="metric__label">Transaction Speed</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">100%</div>
                        <div class="metric__label">dApp Compatibility</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">Zero</div>
                        <div class="metric__label">Code Changes Required</div>
                    </div>
                </div>
            </div>

            <div class="footer__content">
                <div class="footer__brand">
                    <h3 class="footer__logo">zkd.app</h3>
                    <p class="footer__description">
                        The first universal smart contract wallet built on Fluent L2's revolutionary <strong>blended execution network</strong>.
                        Seamless cross-VM execution across EVM, SVM, and WASM with atomic composability through compatibility contracts.
                    </p>
                    <div class="footer__tech">
                        <span class="tech__badge tech__badge--primary">
                            <i class="fas fa-cube"></i>
                            Blended Execution
                        </span>
                        <span class="tech__badge tech__badge--secondary">
                            <i class="fab fa-rust"></i>
                            Rust Core
                        </span>
                        <span class="tech__badge tech__badge--accent">
                            <i class="fas fa-link"></i>
                            Compatibility Contracts
                        </span>
                        <span class="tech__badge">
                            <i class="fas fa-shield-alt"></i>
                            Security Audited
                        </span>
                    </div>
                    <div class="footer__cta">
                        <a href="#early-access" class="btn btn--primary btn--footer">
                            <i class="fas fa-rocket"></i>
                            Join Alpha Program
                        </a>
                    </div>
                </div>
                <div class="footer__links">
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-cube"></i>
                            Product
                        </h4>
                        <a href="#how-it-works" class="footer__link">
                            <i class="fas fa-play-circle"></i>
                            How It Works
                        </a>
                        <a href="#architecture" class="footer__link">
                            <i class="fas fa-sitemap"></i>
                            Technical Architecture
                        </a>
                        <a href="#user-journey" class="footer__link">
                            <i class="fas fa-route"></i>
                            User Experience
                        </a>
                        <a href="#benefits" class="footer__link">
                            <i class="fas fa-star"></i>
                            Key Benefits
                        </a>
                        <a href="#early-access" class="footer__link footer__link--highlight">
                            <i class="fas fa-rocket"></i>
                            Alpha Access
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-code"></i>
                            Developers
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fab fa-github"></i>
                            GitHub Repository
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-book"></i>
                            Technical Documentation
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-plug"></i>
                            Provider Injection API
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-tools"></i>
                            Integration SDK
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-shield-alt"></i>
                            Security Audits
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-flask"></i>
                            Testnet Access
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-users"></i>
                            Community
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fab fa-discord"></i>
                            Discord Server
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-twitter"></i>
                            Twitter Updates
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-telegram"></i>
                            Telegram Group
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-reddit"></i>
                            Reddit Community
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-medium"></i>
                            Technical Blog
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-calendar"></i>
                            Community Events
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-layer-group"></i>
                            Ecosystem
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fas fa-file-alt"></i>
                            Technical Whitepaper
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-road"></i>
                            Development Roadmap
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-handshake"></i>
                            Fluent L2 Partnership
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-chart-line"></i>
                            Performance Metrics
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-palette"></i>
                            Brand Assets
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-gavel"></i>
                            Legal & Privacy
                        </a>
                    </div>
                </div>
            </div>
            <!-- Footer Newsletter -->
            <div class="footer__newsletter">
                <div class="newsletter__content">
                    <div class="newsletter__info">
                        <h4 class="newsletter__title">Stay Updated</h4>
                        <p class="newsletter__description">Get the latest updates on zkd.app development, Fluent L2 integration, and Web3 innovation.</p>
                    </div>
                    <div class="newsletter__form">
                        <div class="form__group">
                            <input type="email" class="form__input" placeholder="Enter your email" aria-label="Email address">
                            <button type="submit" class="btn btn--primary">
                                <i class="fas fa-paper-plane"></i>
                                Subscribe
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer__bottom">
                <div class="footer__bottom-content">
                    <div class="footer__copyright-section">
                        <p class="footer__copyright">
                            &copy; 2024 zkd.app. All rights reserved.
                        </p>
                        <p class="footer__tagline">
                            Built with <i class="fas fa-heart" style="color: var(--accent-color);"></i> for the future of Web3 • Powered by Fluent L2's Blended Execution Network
                        </p>
                    </div>
                    <div class="footer__social">
                        <a href="#" class="footer__social-link footer__social-link--github" aria-label="GitHub Repository">
                            <i class="fab fa-github"></i>
                            <span class="social__tooltip">GitHub</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--twitter" aria-label="Twitter Updates">
                            <i class="fab fa-twitter"></i>
                            <span class="social__tooltip">Twitter</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--discord" aria-label="Discord Community">
                            <i class="fab fa-discord"></i>
                            <span class="social__tooltip">Discord</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--telegram" aria-label="Telegram Group">
                            <i class="fab fa-telegram"></i>
                            <span class="social__tooltip">Telegram</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--medium" aria-label="Technical Blog">
                            <i class="fab fa-medium"></i>
                            <span class="social__tooltip">Medium</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--youtube" aria-label="Video Content">
                            <i class="fab fa-youtube"></i>
                            <span class="social__tooltip">YouTube</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Screen Reader Only Styles -->
    <style>
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles for accessibility */
        .nav__link:focus,
        .btn:focus,
        .nav__toggle:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
            box-shadow: 0 0 0 4px rgba(0, 212, 255, 0.2);
        }

        /* Skip to content link */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: var(--background-dark);
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 10000;
            font-weight: bold;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Form error styles */
        .form__input.error {
            border-color: var(--error-color);
            background: rgba(239, 68, 68, 0.1);
        }

        .field-error {
            color: var(--error-color);
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .field-error::before {
            content: "⚠";
        }

        /* Loading states */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading .btn {
            cursor: not-allowed;
        }

        /* Skeleton loading */
        .skeleton {
            background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            html {
                scroll-behavior: auto;
            }
        }
    </style>

    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Critical JavaScript loaded immediately -->
    <script src="optimized-script.js" defer></script>

    <!-- Non-critical JavaScript loaded after page load -->
    <script>
        window.addEventListener('load', function() {
            // Load analytics after page is fully loaded
            if (typeof gtag !== 'undefined') {
                gtag('config', 'GA_MEASUREMENT_ID', {
                    page_title: 'zkd.app - Universal Wallet',
                    page_location: window.location.href
                });
            }

            // Initialize performance monitoring
            if ('performance' in window && 'measure' in performance) {
                performance.mark('page-load-complete');
                performance.measure('total-load-time', 'navigationStart', 'page-load-complete');
            }
        });

        // Error boundary for JavaScript errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript error:', e.error);
            // Send to error tracking service in production
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            // Send to error tracking service in production
        });
    </script>
</body>
</html>
