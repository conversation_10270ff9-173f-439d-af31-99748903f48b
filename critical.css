/* ===== CRITICAL CSS - ABOVE THE FOLD ONLY ===== */
/* Load immediately for fastest perceived performance */

/* CSS Variables - Essential only */
:root {
  --primary-color: #00D4FF;
  --secondary-color: #8B5CF6;
  --accent-color: #10B981;
  --background-dark: #0A0A0F;
  --background-section: #0F0F1A;
  --text-color: #FFFFFF;
  --text-light: #B8BCC8;
  --font-size-5xl: clamp(2.5rem, 5vw, 4rem);
  --font-size-xl: clamp(1.125rem, 2vw, 1.5rem);
  --font-size-lg: clamp(1rem, 1.5vw, 1.25rem);
  --font-size-base: clamp(0.875rem, 1vw, 1rem);
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-4xl: 6rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* Reset & Base - Critical only */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--background-dark);
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container - Critical */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Header - Critical for above fold */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(10, 10, 15, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transition: var(--transition);
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
}

.nav__logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-decoration: none;
}

.logo__text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  line-height: 1;
}

.logo__tagline {
  font-size: 0.75rem;
  color: var(--text-light);
  font-weight: var(--font-weight-normal);
  margin-top: 2px;
}

.nav__menu {
  display: flex;
  align-items: center;
}

.nav__list {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
  align-items: center;
}

.nav__link {
  color: var(--text-light);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: var(--transition);
  padding: var(--spacing-xs) 0;
}

.nav__link:hover {
  color: var(--primary-color);
}

.nav__link--cta {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark) !important;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
}

.nav__link--cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
}

.nav__toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text-color);
  font-size: var(--font-size-lg);
  cursor: pointer;
}

/* Hero Section - Critical above fold */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: calc(80px + var(--spacing-2xl)) 0 var(--spacing-4xl);
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
  position: relative;
  overflow: hidden;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero__content {
  max-width: 600px;
}

.hero__badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  backdrop-filter: blur(10px);
}

.hero__badge i {
  color: var(--primary-color);
}

.hero__title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  line-height: 1.2;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero__description {
  font-size: var(--font-size-lg);
  color: var(--text-light);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
  max-width: 90%;
}

/* Buttons - Critical */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  transition: var(--transition);
  border: none;
  cursor: pointer;
  white-space: nowrap;
}

.btn--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(0, 212, 255, 0.4);
}

.btn--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn--secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.hero__cta {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

/* Mobile Navigation - Critical for mobile */
@media (max-width: 768px) {
  .nav__menu {
    position: fixed;
    top: 100%;
    left: 0;
    width: 100%;
    background: rgba(10, 10, 15, 0.98);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }

  .nav__menu.show-menu {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav__list {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .nav__toggle {
    display: block;
  }

  .hero__container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero__cta {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .hero__description {
    max-width: 100%;
  }
}

/* Loading optimization */
.hero__visual {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Preload critical fonts */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuFuYAZ9hiA.woff2') format('woff2');
}
