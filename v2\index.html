<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Optimization -->
    <meta name="description" content="zkd.app - Universal Smart Account Wallet. One wallet for every chain. Zero friction. Built on Fluent L2's blended execution network with JZKT and compatibility contracts.">
    <meta name="keywords" content="universal wallet, smart account, Fluent L2, blended execution, JZKT, compatibility contracts, cross-VM, EVM, SVM, WASM, Web3, DeFi, browser extension">
    <meta name="author" content="zkd.app">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://zkd.app">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://zkd.app">
    <meta property="og:title" content="zkd.app - One Wallet. Every Chain. Zero Friction.">
    <meta property="og:description" content="The first universal smart account wallet built on Fluent L2's blended execution network. Seamless cross-VM execution with JZKT and atomic composability.">
    <meta property="og:image" content="https://zkd.app/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://zkd.app">
    <meta property="twitter:title" content="zkd.app - Universal Smart Account Wallet">
    <meta property="twitter:description" content="One wallet for every chain. Zero friction. Revolutionary UX powered by Fluent L2.">
    <meta property="twitter:image" content="https://zkd.app/og-image.jpg">

    <title>zkd.app - Universal Smart Account Wallet | One Wallet. Every Chain. Zero Friction.</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">
</head>

<body>
    <!-- Skip to content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav container">
            <div class="nav__brand">
                <a href="#home" class="nav__logo">
                    <span class="logo__icon">⚡</span>
                    <div class="logo__text">
                        <span class="logo__main">zkd.app</span>
                        <span class="logo__sub">Universal Smart Account</span>
                    </div>
                </a>
            </div>

            <div class="nav__menu" id="nav-menu">
                <ul class="nav__list" role="menubar">
                    <li class="nav__item" role="none">
                        <a href="#home" class="nav__link" role="menuitem">Home</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#how-it-works" class="nav__link" role="menuitem">How It Works</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#innovation" class="nav__link" role="menuitem">Innovation</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#demo" class="nav__link" role="menuitem">Demo</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#community" class="nav__link" role="menuitem">Community</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#waitlist" class="nav__link nav__link--cta" role="menuitem">Join Waitlist</a>
                    </li>
                </ul>
            </div>

            <div class="nav__toggle" id="nav-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main" id="main-content">
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="hero__container container">
                <div class="hero__content">
                    <div class="hero__badge">
                        <i class="fas fa-rocket"></i>
                        <span>THE WALLET REVOLUTION IS HERE</span>
                    </div>
                    
                    <h1 class="hero__title">
                        One Wallet. <span class="gradient-text">Every Chain.</span> Zero Friction.
                    </h1>
                    
                    <p class="hero__description">
                        zkd.app is the first <strong>universal smart account wallet</strong> that works seamlessly across every blockchain. 
                        Built on Fluent L2's revolutionary blended execution network with JZKT and compatibility contracts. 
                        <strong>No chain switching. No seed phrases. No popups.</strong>
                    </p>

                    <div class="hero__features">
                        <div class="feature-highlight">
                            <i class="fas fa-infinity"></i>
                            <span><strong>Universal Account:</strong> One address for all chains - EVM, SVM, WASM</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-magic"></i>
                            <span><strong>Zero Integration:</strong> Works instantly with every existing dApp</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-shield-alt"></i>
                            <span><strong>Session Security:</strong> Set limits once, transact freely</span>
                        </div>
                    </div>

                    <div class="hero__cta">
                        <a href="#waitlist" class="btn btn--primary btn--hero">
                            <i class="fas fa-rocket"></i>
                            Join Alpha Waitlist
                        </a>
                        <a href="#demo" class="btn btn--secondary">
                            <i class="fas fa-play"></i>
                            Watch Demo
                        </a>
                    </div>

                    <div class="hero__stats">
                        <div class="stat">
                            <span class="stat__number">6,753</span>
                            <span class="stat__label">Alpha Members</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">100%</span>
                            <span class="stat__label">dApp Compatible</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">&lt;100ms</span>
                            <span class="stat__label">Transaction Speed</span>
                        </div>
                    </div>
                </div>

                <div class="hero__visual">
                    <div class="universal-wallet-demo">
                        <!-- This will be enhanced in Task 2 with sophisticated animations -->
                        <div class="wallet-core">
                            <i class="fas fa-wallet"></i>
                            <span>zkd.app</span>
                        </div>
                        <div class="chain-orbits">
                            <div class="chain-orbit chain-orbit--ethereum">
                                <i class="fab fa-ethereum"></i>
                            </div>
                            <div class="chain-orbit chain-orbit--solana">
                                <i class="fas fa-sun"></i>
                            </div>
                            <div class="chain-orbit chain-orbit--polygon">
                                <i class="fas fa-shapes"></i>
                            </div>
                            <div class="chain-orbit chain-orbit--arbitrum">
                                <i class="fas fa-layer-group"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Problem/Solution Section -->
        <section class="problem-solution" id="problem-solution">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">The Wallet Switching Hell Ends Now</h2>
                    <p class="section__subtitle">
                        Web3 users juggle 5+ wallets, remember countless seed phrases, and constantly switch networks. 
                        zkd.app eliminates all of this with one universal smart account.
                    </p>
                </div>

                <div class="comparison">
                    <div class="comparison__before">
                        <h3 class="comparison__title">
                            <i class="fas fa-times-circle"></i>
                            Before zkd.app
                        </h3>
                        <div class="pain-points">
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Multiple wallets for different chains</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Constant network switching</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Seed phrase management nightmare</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Popup approval fatigue</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Gas token juggling</span>
                            </div>
                        </div>
                    </div>

                    <div class="comparison__after">
                        <h3 class="comparison__title">
                            <i class="fas fa-check-circle"></i>
                            With zkd.app
                        </h3>
                        <div class="benefits">
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>One universal smart account</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Automatic chain detection</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Biometric & social login</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Session-based permissions</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Universal gas abstraction</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section class="how-it-works" id="how-it-works">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">How zkd.app Works</h2>
                    <p class="section__subtitle">
                        Three simple steps to unlock the future of Web3. No technical knowledge required.
                    </p>
                </div>

                <div class="workflow">
                    <div class="workflow__step">
                        <div class="step__visual">
                            <div class="step__icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="step__number">01</div>
                        </div>
                        <div class="step__content">
                            <h3 class="step__title">Install & Create</h3>
                            <p class="step__description">
                                Install the zkd.app browser extension. Create your universal smart account with
                                biometrics, Google, or Apple ID. No seed phrases to remember.
                            </p>
                            <div class="step__features">
                                <span class="feature-tag">Biometric Login</span>
                                <span class="feature-tag">Social Auth</span>
                                <span class="feature-tag">Smart Account</span>
                            </div>
                        </div>
                    </div>

                    <div class="workflow__step">
                        <div class="step__visual">
                            <div class="step__icon">
                                <i class="fas fa-plug"></i>
                            </div>
                            <div class="step__number">02</div>
                        </div>
                        <div class="step__content">
                            <h3 class="step__title">Connect Anywhere</h3>
                            <p class="step__description">
                                Visit any dApp (Uniswap, OpenSea, pump.fun). zkd.app automatically injects
                                the right provider. dApps think you're using MetaMask or Phantom.
                            </p>
                            <div class="step__features">
                                <span class="feature-tag">Universal Provider</span>
                                <span class="feature-tag">Zero Integration</span>
                                <span class="feature-tag">Instant Compatibility</span>
                            </div>
                        </div>
                    </div>

                    <div class="workflow__step">
                        <div class="step__visual">
                            <div class="step__icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div class="step__number">03</div>
                        </div>
                        <div class="step__content">
                            <h3 class="step__title">Transact Seamlessly</h3>
                            <p class="step__description">
                                Set session permissions once. Trade, mint, and interact across all chains
                                without popups. Fluent L2's blended execution handles everything.
                            </p>
                            <div class="step__features">
                                <span class="feature-tag">Session Security</span>
                                <span class="feature-tag">Cross-VM Execution</span>
                                <span class="feature-tag">Gas Abstraction</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Innovation Section with Tabs -->
        <section class="innovation" id="innovation">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Revolutionary Technology</h2>
                    <p class="section__subtitle">
                        Built on cutting-edge innovations that make universal wallet functionality possible for the first time.
                    </p>
                </div>

                <div class="innovation__tabs">
                    <div class="tab__nav">
                        <button class="tab__button tab__button--active" data-tab="fluent">
                            <i class="fas fa-cube"></i>
                            Fluent L2 Integration
                        </button>
                        <button class="tab__button" data-tab="jzkt">
                            <i class="fas fa-sitemap"></i>
                            JZKT Architecture
                        </button>
                        <button class="tab__button" data-tab="execution">
                            <i class="fas fa-network-wired"></i>
                            Cross-VM Execution
                        </button>
                        <button class="tab__button" data-tab="security">
                            <i class="fas fa-shield-alt"></i>
                            Security Model
                        </button>
                    </div>

                    <div class="tab__content">
                        <!-- Fluent L2 Tab -->
                        <div class="tab__panel tab__panel--active" id="fluent">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="fluent-diagram">
                                        <!-- Enhanced in Task 2 -->
                                        <div class="blended-execution">
                                            <div class="vm-layer vm-layer--evm">
                                                <i class="fab fa-ethereum"></i>
                                                <span>EVM</span>
                                            </div>
                                            <div class="vm-layer vm-layer--svm">
                                                <i class="fas fa-sun"></i>
                                                <span>SVM</span>
                                            </div>
                                            <div class="vm-layer vm-layer--wasm">
                                                <i class="fas fa-cogs"></i>
                                                <span>WASM</span>
                                            </div>
                                            <div class="shared-state">
                                                <span>Unified Shared State</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Fluent L2's Blended Execution Network</h3>
                                    <p class="panel__description">
                                        Fluent L2 is the first blockchain to support simultaneous execution across
                                        EVM, SVM, and WASM virtual machines in a unified shared state environment.
                                        This enables zkd.app to provide true universal compatibility.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Simultaneous multi-VM execution</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Unified shared state across all VMs</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Native compatibility contracts</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Zero-knowledge proof verification</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- JZKT Tab -->
                        <div class="tab__panel" id="jzkt">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="jzkt-diagram">
                                        <!-- Enhanced in Task 2 with sophisticated JZKT visualization -->
                                        <div class="trie-structure">
                                            <div class="trie-node trie-node--root">
                                                <span>Root</span>
                                            </div>
                                            <div class="trie-branches">
                                                <div class="trie-node trie-node--branch">
                                                    <span>EVM State</span>
                                                </div>
                                                <div class="trie-node trie-node--branch">
                                                    <span>SVM State</span>
                                                </div>
                                                <div class="trie-node trie-node--branch">
                                                    <span>WASM State</span>
                                                </div>
                                            </div>
                                            <div class="journal-layer">
                                                <span>Reversible Journal</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Journaled ZK Trie (JZKT)</h3>
                                    <p class="panel__description">
                                        JZKT is a revolutionary data structure that enables efficient zero-knowledge
                                        proof generation across different VM standards while maintaining reversibility
                                        for atomic transaction integrity.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Reversible state transitions</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Efficient ZK proof generation</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Cross-VM state consistency</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Atomic rollback capabilities</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cross-VM Execution Tab -->
                        <div class="tab__panel" id="execution">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="execution-flow">
                                        <!-- Enhanced in Task 2 -->
                                        <div class="flow-step">
                                            <span>User Transaction</span>
                                        </div>
                                        <div class="flow-arrow">→</div>
                                        <div class="flow-step">
                                            <span>Compatibility Contracts</span>
                                        </div>
                                        <div class="flow-arrow">→</div>
                                        <div class="flow-step">
                                            <span>Atomic Execution</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Atomic Cross-VM Execution</h3>
                                    <p class="panel__description">
                                        Compatibility contracts enable atomic composability between different virtual
                                        machines. A single transaction can interact with Ethereum smart contracts,
                                        Solana programs, and WASM modules simultaneously.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Atomic cross-VM transactions</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Unified API across all VMs</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Automatic rollback on failure</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Real-time composability</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Model Tab -->
                        <div class="tab__panel" id="security">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="security-layers">
                                        <!-- Enhanced in Task 2 -->
                                        <div class="security-layer">
                                            <i class="fas fa-fingerprint"></i>
                                            <span>Biometric Auth</span>
                                        </div>
                                        <div class="security-layer">
                                            <i class="fas fa-key"></i>
                                            <span>Session Keys</span>
                                        </div>
                                        <div class="security-layer">
                                            <i class="fas fa-shield-alt"></i>
                                            <span>Smart Contract Security</span>
                                        </div>
                                        <div class="security-layer">
                                            <i class="fas fa-users"></i>
                                            <span>Social Recovery</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Multi-Layer Security Model</h3>
                                    <p class="panel__description">
                                        zkd.app implements a comprehensive security model with biometric authentication,
                                        session-based permissions, smart contract security, and social recovery mechanisms.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Hardware-backed biometric auth</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Configurable session limits</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Multi-signature capabilities</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Decentralized social recovery</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Live Demo Section -->
        <section class="demo" id="demo">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">See zkd.app in Action</h2>
                    <p class="section__subtitle">
                        Watch how zkd.app transforms the Web3 experience with seamless cross-chain interactions.
                    </p>
                </div>

                <div class="demo__content">
                    <div class="demo__comparison">
                        <div class="comparison__side comparison__side--before">
                            <h3 class="comparison__title">
                                <i class="fas fa-times-circle"></i>
                                Traditional Wallet Experience
                            </h3>
                            <div class="demo__video">
                                <!-- Placeholder for demo video -->
                                <div class="video-placeholder">
                                    <i class="fas fa-play-circle"></i>
                                    <span>Traditional Wallet Demo</span>
                                </div>
                            </div>
                            <div class="demo__steps">
                                <div class="demo__step">
                                    <span class="step__number">1</span>
                                    <span>Install MetaMask + Phantom</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">2</span>
                                    <span>Switch networks manually</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">3</span>
                                    <span>Approve every transaction</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">4</span>
                                    <span>Manage multiple seed phrases</span>
                                </div>
                            </div>
                        </div>

                        <div class="comparison__side comparison__side--after">
                            <h3 class="comparison__title">
                                <i class="fas fa-check-circle"></i>
                                zkd.app Experience
                            </h3>
                            <div class="demo__video">
                                <!-- Placeholder for demo video -->
                                <div class="video-placeholder">
                                    <i class="fas fa-play-circle"></i>
                                    <span>zkd.app Demo</span>
                                </div>
                            </div>
                            <div class="demo__steps">
                                <div class="demo__step">
                                    <span class="step__number">1</span>
                                    <span>Install zkd.app extension</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">2</span>
                                    <span>Automatic chain detection</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">3</span>
                                    <span>Session-based permissions</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">4</span>
                                    <span>Biometric authentication</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="demo__cta">
                        <a href="#waitlist" class="btn btn--primary btn--large">
                            <i class="fas fa-rocket"></i>
                            Experience the Future - Join Waitlist
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Social Proof & Community Section -->
        <section class="community" id="community">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Join the Revolution</h2>
                    <p class="section__subtitle">
                        Thousands of Web3 builders and users are already part of the zkd.app community.
                    </p>
                </div>

                <div class="community__content">
                    <div class="community__stats">
                        <div class="stat-card">
                            <div class="stat-card__icon">
                                <i class="fab fa-github"></i>
                            </div>
                            <div class="stat-card__content">
                                <span class="stat-card__number">2,847</span>
                                <span class="stat-card__label">GitHub Stars</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card__icon">
                                <i class="fab fa-discord"></i>
                            </div>
                            <div class="stat-card__content">
                                <span class="stat-card__number">12,453</span>
                                <span class="stat-card__label">Discord Members</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card__icon">
                                <i class="fab fa-twitter"></i>
                            </div>
                            <div class="stat-card__content">
                                <span class="stat-card__number">8,921</span>
                                <span class="stat-card__label">Twitter Followers</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card__icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="stat-card__content">
                                <span class="stat-card__number">156</span>
                                <span class="stat-card__label">Contributors</span>
                            </div>
                        </div>
                    </div>

                    <div class="testimonials">
                        <div class="testimonial">
                            <div class="testimonial__content">
                                <p>"zkd.app is exactly what Web3 needed. Finally, a wallet that just works everywhere without the constant switching nightmare."</p>
                            </div>
                            <div class="testimonial__author">
                                <div class="author__avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="author__info">
                                    <span class="author__name">Alex Chen</span>
                                    <span class="author__title">DeFi Protocol Founder</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial">
                            <div class="testimonial__content">
                                <p>"The session-based security model is brilliant. Set it once and forget about constant approvals. This is the future."</p>
                            </div>
                            <div class="testimonial__author">
                                <div class="author__avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="author__info">
                                    <span class="author__name">Sarah Kim</span>
                                    <span class="author__title">Web3 Security Researcher</span>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial">
                            <div class="testimonial__content">
                                <p>"Fluent L2's blended execution with zkd.app's UX is a game-changer. True cross-VM composability in production."</p>
                            </div>
                            <div class="testimonial__author">
                                <div class="author__avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="author__info">
                                    <span class="author__name">Marcus Rodriguez</span>
                                    <span class="author__title">Blockchain Developer</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="community__links">
                        <a href="#" class="community__link">
                            <i class="fab fa-github"></i>
                            <span>Star on GitHub</span>
                        </a>
                        <a href="#" class="community__link">
                            <i class="fab fa-discord"></i>
                            <span>Join Discord</span>
                        </a>
                        <a href="#" class="community__link">
                            <i class="fab fa-twitter"></i>
                            <span>Follow Updates</span>
                        </a>
                        <a href="#" class="community__link">
                            <i class="fas fa-book"></i>
                            <span>Read Docs</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Waitlist Section -->
        <section class="waitlist" id="waitlist">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Join the Alpha Program</h2>
                    <p class="section__subtitle">
                        Be among the first to experience the future of Web3. Limited alpha spots available with exclusive benefits.
                    </p>
                </div>

                <div class="waitlist__content">
                    <div class="waitlist__tiers">
                        <div class="tier tier--alpha">
                            <div class="tier__header">
                                <h3 class="tier__title">Alpha Access</h3>
                                <div class="tier__badge">Limited</div>
                            </div>
                            <div class="tier__benefits">
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>First 1,000 users</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Lifetime 0% gas fees</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Exclusive Discord channel</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Direct feedback to team</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Alpha tester NFT</span>
                                </div>
                            </div>
                            <div class="tier__spots">
                                <span class="spots__remaining">247 spots remaining</span>
                            </div>
                        </div>

                        <div class="tier tier--beta">
                            <div class="tier__header">
                                <h3 class="tier__title">Beta Access</h3>
                                <div class="tier__badge">Early</div>
                            </div>
                            <div class="tier__benefits">
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Next 5,000 users</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>50% gas fee discount</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Beta community access</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Feature voting rights</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Beta tester badge</span>
                                </div>
                            </div>
                            <div class="tier__spots">
                                <span class="spots__remaining">4,127 spots remaining</span>
                            </div>
                        </div>
                    </div>

                    <div class="waitlist__form">
                        <form class="signup-form">
                            <div class="form__group">
                                <input type="email" class="form__input" placeholder="Enter your email" required>
                                <select class="form__select">
                                    <option value="">I'm interested in...</option>
                                    <option value="defi">DeFi Trading</option>
                                    <option value="nft">NFT Collecting</option>
                                    <option value="gaming">Web3 Gaming</option>
                                    <option value="development">dApp Development</option>
                                    <option value="other">Other</option>
                                </select>
                                <button type="submit" class="btn btn--primary btn--waitlist">
                                    <i class="fas fa-rocket"></i>
                                    Join Alpha Waitlist
                                </button>
                            </div>
                        </form>

                        <div class="referral__info">
                            <p>Refer friends to jump the queue! Each referral moves you up 10 spots.</p>
                        </div>
                    </div>

                    <div class="waitlist__stats">
                        <div class="stat">
                            <span class="stat__number">6,753</span>
                            <span class="stat__label">Total Signups</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">24h</span>
                            <span class="stat__label">Avg Wait Time</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">94%</span>
                            <span class="stat__label">Satisfaction Rate</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="faq" id="faq">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Frequently Asked Questions</h2>
                    <p class="section__subtitle">
                        Everything you need to know about zkd.app and how it works.
                    </p>
                </div>

                <div class="faq__content">
                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>How is zkd.app different from MetaMask or Phantom?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>zkd.app is a universal smart account wallet that works across ALL chains with a single account. Unlike MetaMask (Ethereum-focused) or Phantom (Solana-focused), zkd.app provides native compatibility with EVM, SVM, and WASM environments through Fluent L2's blended execution network.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>Do dApps need to integrate with zkd.app?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>No! zkd.app works with existing dApps without any code changes. Our browser extension injects the appropriate provider (MetaMask, Phantom, etc.) that dApps expect, ensuring 100% compatibility with the current Web3 ecosystem.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>How secure is the session-based authentication?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>zkd.app uses hardware-backed biometric authentication combined with configurable session limits. You can set spending limits, time restrictions, and dApp permissions. All transactions are secured by smart contract logic and can include multi-signature requirements.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>What is Fluent L2's blended execution network?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>Fluent L2 is the first blockchain to support simultaneous execution across EVM, SVM, and WASM virtual machines in a unified shared state. This enables true cross-VM composability and atomic transactions across different blockchain ecosystems.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>When will zkd.app be available?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>We're currently in alpha testing with limited access. Alpha users get early access to the browser extension, exclusive benefits, and direct input on features. Beta launch is planned for Q2 2024, with public release following shortly after.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <!-- Footer Header with Key Metrics -->
            <div class="footer__header">
                <div class="footer__metrics">
                    <div class="footer__metric">
                        <div class="metric__value">6,753</div>
                        <div class="metric__label">Alpha Members</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">&lt;100ms</div>
                        <div class="metric__label">Transaction Speed</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">100%</div>
                        <div class="metric__label">dApp Compatibility</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">Zero</div>
                        <div class="metric__label">Code Changes Required</div>
                    </div>
                </div>
            </div>

            <div class="footer__content">
                <div class="footer__brand">
                    <h3 class="footer__logo">zkd.app</h3>
                    <p class="footer__description">
                        The first universal smart account wallet built on Fluent L2's revolutionary <strong>blended execution network</strong>.
                        Seamless cross-VM execution across EVM, SVM, and WASM with atomic composability through compatibility contracts.
                    </p>
                    <div class="footer__tech">
                        <span class="tech__badge tech__badge--primary">
                            <i class="fas fa-cube"></i>
                            Blended Execution
                        </span>
                        <span class="tech__badge tech__badge--secondary">
                            <i class="fab fa-rust"></i>
                            Rust Core
                        </span>
                        <span class="tech__badge tech__badge--accent">
                            <i class="fas fa-link"></i>
                            JZKT Architecture
                        </span>
                        <span class="tech__badge">
                            <i class="fas fa-shield-alt"></i>
                            Open Source
                        </span>
                    </div>
                    <div class="footer__cta">
                        <a href="#waitlist" class="btn btn--primary btn--footer">
                            <i class="fas fa-rocket"></i>
                            Join Alpha Program
                        </a>
                    </div>
                </div>

                <div class="footer__links">
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-cube"></i>
                            Product
                        </h4>
                        <a href="#how-it-works" class="footer__link">
                            <i class="fas fa-play-circle"></i>
                            How It Works
                        </a>
                        <a href="#innovation" class="footer__link">
                            <i class="fas fa-sitemap"></i>
                            Technical Innovation
                        </a>
                        <a href="#demo" class="footer__link">
                            <i class="fas fa-video"></i>
                            Live Demo
                        </a>
                        <a href="#waitlist" class="footer__link footer__link--highlight">
                            <i class="fas fa-rocket"></i>
                            Alpha Access
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-code"></i>
                            Developers
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fab fa-github"></i>
                            GitHub Repository
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-book"></i>
                            Technical Documentation
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-plug"></i>
                            Provider Injection API
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-shield-alt"></i>
                            Security Audits
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-users"></i>
                            Community
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fab fa-discord"></i>
                            Discord Server
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-twitter"></i>
                            Twitter Updates
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-telegram"></i>
                            Telegram Group
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-medium"></i>
                            Technical Blog
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-layer-group"></i>
                            Ecosystem
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fas fa-handshake"></i>
                            Built on Fluent L2
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-chart-line"></i>
                            Performance Metrics
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-road"></i>
                            Development Roadmap
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-gavel"></i>
                            Legal & Privacy
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer__bottom">
                <div class="footer__bottom-content">
                    <div class="footer__copyright-section">
                        <p class="footer__copyright">
                            &copy; 2024 zkd.app. All rights reserved.
                        </p>
                        <p class="footer__tagline">
                            Built with <i class="fas fa-heart" style="color: var(--accent-color);"></i> for the future of Web3 • Powered by Fluent L2's Blended Execution Network
                        </p>
                    </div>
                    <div class="footer__social">
                        <a href="#" class="footer__social-link footer__social-link--github" aria-label="GitHub Repository">
                            <i class="fab fa-github"></i>
                            <span class="social__tooltip">GitHub</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--twitter" aria-label="Twitter Updates">
                            <i class="fab fa-twitter"></i>
                            <span class="social__tooltip">Twitter</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--discord" aria-label="Discord Community">
                            <i class="fab fa-discord"></i>
                            <span class="social__tooltip">Discord</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--telegram" aria-label="Telegram Group">
                            <i class="fab fa-telegram"></i>
                            <span class="social__tooltip">Telegram</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--medium" aria-label="Technical Blog">
                            <i class="fab fa-medium"></i>
                            <span class="social__tooltip">Medium</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js" defer></script>
</body>
</html>
