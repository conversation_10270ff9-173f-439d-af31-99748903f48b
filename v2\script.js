// ===== ZKDAPP LANDING PAGE JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initTabs();
    initFAQ();
    initWaitlistForm();
    initScrollEffects();
    initAnimations();
});

// ===== NAVIGATION =====
function initNavigation() {
    const header = document.getElementById('header');
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav__link');

    // Header scroll effect
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('header--scrolled');
        } else {
            header.classList.remove('header--scrolled');
        }
    });

    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('nav__menu--active');
            navToggle.classList.toggle('nav__toggle--active');
        });
    }

    // Smooth scroll for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // Close mobile menu if open
                navMenu.classList.remove('nav__menu--active');
                navToggle.classList.remove('nav__toggle--active');
            }
        });
    });

    // Active navigation highlighting
    const sections = document.querySelectorAll('section[id]');
    window.addEventListener('scroll', () => {
        const scrollY = window.pageYOffset;
        sections.forEach(section => {
            const sectionHeight = section.offsetHeight;
            const sectionTop = section.offsetTop - 100;
            const sectionId = section.getAttribute('id');
            const navLink = document.querySelector(`.nav__link[href="#${sectionId}"]`);
            
            if (scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
                navLinks.forEach(link => link.classList.remove('nav__link--active'));
                if (navLink) navLink.classList.add('nav__link--active');
            }
        });
    });
}

// ===== TABS FUNCTIONALITY =====
function initTabs() {
    const tabButtons = document.querySelectorAll('.tab__button');
    const tabPanels = document.querySelectorAll('.tab__panel');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('tab__button--active'));
            tabPanels.forEach(panel => panel.classList.remove('tab__panel--active'));
            
            // Add active class to clicked button and corresponding panel
            button.classList.add('tab__button--active');
            const targetPanel = document.getElementById(targetTab);
            if (targetPanel) {
                targetPanel.classList.add('tab__panel--active');
            }
        });
    });
}

// ===== FAQ ACCORDION =====
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq__item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq__question');
        const answer = item.querySelector('.faq__answer');
        const icon = question.querySelector('i');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('faq__item--active');
            
            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('faq__item--active');
                const faqIcon = faqItem.querySelector('.faq__question i');
                faqIcon.style.transform = 'rotate(0deg)';
            });
            
            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('faq__item--active');
                icon.style.transform = 'rotate(180deg)';
            }
        });
    });
}

// ===== WAITLIST FORM =====
function initWaitlistForm() {
    const forms = document.querySelectorAll('.signup-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            const email = form.querySelector('input[type="email"]').value;
            const interest = form.querySelector('select')?.value || '';
            
            if (email) {
                // Simulate form submission
                const button = form.querySelector('button[type="submit"]');
                const originalText = button.innerHTML;
                
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Joining...';
                button.disabled = true;
                
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-check"></i> Welcome to Alpha!';
                    button.style.background = 'var(--accent-color)';
                    
                    // Show success message
                    showNotification('Successfully joined the alpha waitlist!', 'success');
                    
                    // Reset form after delay
                    setTimeout(() => {
                        form.reset();
                        button.innerHTML = originalText;
                        button.disabled = false;
                        button.style.background = '';
                    }, 3000);
                }, 2000);
            }
        });
    });
}

// ===== SCROLL EFFECTS =====
function initScrollEffects() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll(
        '.workflow__step, .stat-card, .testimonial, .tier, .faq__item'
    );
    
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// ===== ANIMATIONS =====
function initAnimations() {
    // Counter animation for stats
    const counters = document.querySelectorAll('.stat__number, .metric__value');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = counter.textContent.replace(/\d+/, target);
                clearInterval(timer);
            } else {
                counter.textContent = counter.textContent.replace(/\d+/, Math.floor(current));
            }
        }, 16);
    };

    // Trigger counter animations when in view
    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                entry.target.classList.add('animated');
                animateCounter(entry.target);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });

    // Floating animation for hero visual elements
    const floatingElements = document.querySelectorAll('.chain-orbit');
    floatingElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.5}s`;
        element.classList.add('floating');
    });
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification--${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('notification--show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('notification--show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Debounce function for scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function for resize events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
// Keyboard navigation for custom elements
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        // Close mobile menu
        const navMenu = document.getElementById('nav-menu');
        const navToggle = document.getElementById('nav-toggle');
        navMenu.classList.remove('nav__menu--active');
        navToggle.classList.remove('nav__toggle--active');
    }
});

// Focus management for tab navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
    }
});

document.addEventListener('mousedown', () => {
    document.body.classList.remove('keyboard-navigation');
});

// ===== ENHANCED ANIMATIONS & INTERACTIONS =====

// Subtle background enhancement (removed flashy particle system)
function initBackgroundEnhancement() {
    // Keep it simple - just add a subtle gradient shift
    const hero = document.querySelector('.hero');
    if (!hero) return;

    // Add subtle background animation
    hero.style.backgroundSize = '200% 200%';
    hero.style.animation = 'gradientShift 20s ease infinite';

    // Add CSS animation if not already added
    if (!document.querySelector('#background-animations')) {
        const style = document.createElement('style');
        style.id = 'background-animations';
        style.textContent = `
            @keyframes gradientShift {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }
        `;
        document.head.appendChild(style);
    }
}

// Enhanced scroll-triggered animations
function initAdvancedScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;

                // Add staggered animation delays for child elements
                const children = element.querySelectorAll('.workflow__step, .stat-card, .testimonial, .tier, .feature-item');
                children.forEach((child, index) => {
                    child.style.animationDelay = `${index * 0.1}s`;
                    child.classList.add('animate-in');
                });

                element.classList.add('animate-in');

                // Special animations for specific elements
                if (element.classList.contains('trie-structure')) {
                    animateTrieStructure(element);
                } else if (element.classList.contains('execution-flow')) {
                    animateExecutionFlow(element);
                } else if (element.classList.contains('security-layers')) {
                    animateSecurityLayers(element);
                }
            }
        });
    }, observerOptions);

    // Observe all animatable elements
    const animateElements = document.querySelectorAll(`
        .workflow, .innovation__tabs, .demo__comparison, .community__stats,
        .testimonials, .waitlist__tiers, .faq__content, .trie-structure,
        .execution-flow, .security-layers, .blended-execution
    `);

    animateElements.forEach(el => observer.observe(el));
}

// JZKT Trie Structure Animation
function animateTrieStructure(element) {
    const root = element.querySelector('.trie-node--root');
    const branches = element.querySelectorAll('.trie-node--branch');
    const journal = element.querySelector('.journal-layer');

    if (root) {
        setTimeout(() => root.style.transform = 'scale(1.1)', 200);
        setTimeout(() => root.style.transform = 'scale(1)', 400);
    }

    branches.forEach((branch, index) => {
        setTimeout(() => {
            branch.style.transform = 'scale(1.05)';
            branch.style.boxShadow = '0 8px 25px rgba(139, 92, 246, 0.5)';
        }, 600 + index * 200);

        setTimeout(() => {
            branch.style.transform = 'scale(1)';
            branch.style.boxShadow = '0 4px 15px rgba(139, 92, 246, 0.3)';
        }, 800 + index * 200);
    });

    if (journal) {
        setTimeout(() => {
            journal.style.animation = 'journalFlow 2s ease-in-out infinite';
        }, 1200);
    }
}

// Execution Flow Animation
function animateExecutionFlow(element) {
    const steps = element.querySelectorAll('.flow-step');
    const arrows = element.querySelectorAll('.flow-arrow');

    steps.forEach((step, index) => {
        setTimeout(() => {
            step.style.transform = 'scale(1.1)';
            step.style.boxShadow = '0 8px 25px rgba(0, 212, 255, 0.4)';
        }, index * 300);

        setTimeout(() => {
            step.style.transform = 'scale(1)';
        }, index * 300 + 200);
    });

    arrows.forEach((arrow, index) => {
        setTimeout(() => {
            arrow.style.animation = 'arrowPulse 1s ease-in-out infinite';
        }, index * 300 + 150);
    });
}

// Security Layers Animation
function animateSecurityLayers(element) {
    const layers = element.querySelectorAll('.security-layer');

    layers.forEach((layer, index) => {
        setTimeout(() => {
            layer.style.transform = 'translateY(-10px) scale(1.05)';
            layer.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.3)';
        }, index * 200);

        setTimeout(() => {
            layer.style.transform = 'translateY(0) scale(1)';
            layer.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
        }, index * 200 + 300);
    });
}

// Enhanced tab switching with smooth transitions
function initEnhancedTabs() {
    const tabButtons = document.querySelectorAll('.tab__button');
    const tabPanels = document.querySelectorAll('.tab__panel');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('tab__button--active'));
            tabPanels.forEach(panel => {
                panel.classList.remove('tab__panel--active');
                panel.style.opacity = '0';
                panel.style.transform = 'translateY(20px)';
            });

            // Add active class to clicked button
            button.classList.add('tab__button--active');

            // Show target panel with animation
            const targetPanel = document.getElementById(targetTab);
            if (targetPanel) {
                setTimeout(() => {
                    targetPanel.classList.add('tab__panel--active');
                    targetPanel.style.opacity = '1';
                    targetPanel.style.transform = 'translateY(0)';
                }, 150);
            }
        });
    });
}

// Simple title enhancement (removed distracting typing animation)
function initTitleEnhancement() {
    const heroTitle = document.querySelector('.hero__title');
    if (!heroTitle) return;

    // Just add a subtle fade-in effect
    heroTitle.style.opacity = '0';
    setTimeout(() => {
        heroTitle.style.transition = 'opacity 1s ease';
        heroTitle.style.opacity = '1';
    }, 500);
}

// Enhanced counter animations with easing
function initEnhancedCounters() {
    const counters = document.querySelectorAll('.stat__number, .metric__value, .stat-card__number');

    const animateCounter = (counter) => {
        const text = counter.textContent;
        const target = parseInt(text.replace(/[^\d]/g, ''));
        const prefix = text.replace(/[\d]/g, '');
        const duration = 2000;
        const startTime = performance.now();

        function updateCounter(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = Math.floor(target * easeOut);

            counter.textContent = prefix + current.toLocaleString();

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = text; // Restore original text
            }
        }

        requestAnimationFrame(updateCounter);
    };

    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                entry.target.classList.add('animated');
                setTimeout(() => animateCounter(entry.target), Math.random() * 500);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => counterObserver.observe(counter));
}

// Subtle button enhancement (toned down magnetic effect)
function initButtonEnhancements() {
    const buttons = document.querySelectorAll('.btn--primary, .btn--hero');

    buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-2px)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = 'translateY(0px)';
        });
    });
}

// Initialize all enhanced features
document.addEventListener('DOMContentLoaded', function() {
    // Initialize original functionality
    initNavigation();
    initEnhancedTabs(); // Use enhanced version
    initFAQ();
    initWaitlistForm();
    initScrollEffects();
    initAnimations();

    // Initialize new enhanced features (toned down)
    initBackgroundEnhancement();
    initAdvancedScrollAnimations();
    initTitleEnhancement();
    initEnhancedCounters();
    initButtonEnhancements();

    // Add smooth reveal animation to page
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.5s ease';
        document.body.style.opacity = '1';
    }, 100);
});
