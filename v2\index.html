<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Optimization -->
    <meta name="description" content="zkd.app - Universal Smart Account Wallet. One wallet for every chain. Zero friction. Built on Fluent L2's blended execution network with JZKT and compatibility contracts.">
    <meta name="keywords" content="universal wallet, smart account, Fluent L2, blended execution, JZKT, compatibility contracts, cross-VM, EVM, SVM, WASM, Web3, DeFi, browser extension">
    <meta name="author" content="zkd.app">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://zkd.app">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://zkd.app">
    <meta property="og:title" content="zkd.app - One Wallet. Every Chain. Zero Friction.">
    <meta property="og:description" content="The first universal smart account wallet built on Fluent L2's blended execution network. Seamless cross-VM execution with JZKT and atomic composability.">
    <meta property="og:image" content="https://zkd.app/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://zkd.app">
    <meta property="twitter:title" content="zkd.app - Universal Smart Account Wallet">
    <meta property="twitter:description" content="One wallet for every chain. Zero friction. Revolutionary UX powered by Fluent L2.">
    <meta property="twitter:image" content="https://zkd.app/og-image.jpg">

    <title>zkd.app - Universal Smart Account Wallet | One Wallet. Every Chain. Zero Friction.</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="styles.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">
</head>

<body>
    <!-- Skip to content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header -->
    <header class="header" id="header">
        <nav class="nav container">
            <div class="nav__brand">
                <a href="#home" class="nav__logo">
                    <span class="logo__icon">⚡</span>
                    <div class="logo__text">
                        <span class="logo__main">zkd.app</span>
                        <span class="logo__sub">Universal Smart Account</span>
                    </div>
                </a>
            </div>

            <div class="nav__menu" id="nav-menu">
                <ul class="nav__list" role="menubar">
                    <li class="nav__item" role="none">
                        <a href="#home" class="nav__link" role="menuitem">Home</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#how-it-works" class="nav__link" role="menuitem">How It Works</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#innovation" class="nav__link" role="menuitem">Innovation</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#demo" class="nav__link" role="menuitem">Demo</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#community" class="nav__link" role="menuitem">Community</a>
                    </li>
                    <li class="nav__item" role="none">
                        <a href="#waitlist" class="nav__link nav__link--cta" role="menuitem">Join Waitlist</a>
                    </li>
                </ul>
            </div>

            <div class="nav__toggle" id="nav-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main" id="main-content">
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="hero__container container">
                <div class="hero__content">
                    <div class="hero__badge">
                        <i class="fas fa-lightbulb"></i>
                        <span>BUILDING THE FUTURE OF WEB3 UX</span>
                    </div>

                    <h1 class="hero__title">
                        The Universal Wallet <span class="gradient-text">Vision</span>
                    </h1>

                    <p class="hero__description">
                        We're building the <strong>universal smart account wallet</strong> that will work seamlessly across every blockchain.
                        Powered by next-generation blended execution technology and revolutionary UX design.
                        <strong>One account. Every chain. Zero friction.</strong>
                    </p>

                    <div class="hero__features">
                        <div class="feature-highlight">
                            <i class="fas fa-infinity"></i>
                            <span><strong>Universal Vision:</strong> One account for all chains and ecosystems</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-magic"></i>
                            <span><strong>Zero Friction:</strong> Seamless UX that just works everywhere</span>
                        </div>
                        <div class="feature-highlight">
                            <i class="fas fa-users"></i>
                            <span><strong>Community Driven:</strong> Built by builders, for the future</span>
                        </div>
                    </div>

                    <div class="hero__cta">
                        <a href="#community" class="btn btn--primary btn--hero">
                            <i class="fas fa-users"></i>
                            Join the Vision
                        </a>
                        <a href="#innovation" class="btn btn--secondary">
                            <i class="fas fa-cogs"></i>
                            Explore Tech
                        </a>
                    </div>

                    <div class="hero__stats">
                        <div class="stat">
                            <span class="stat__number">Early</span>
                            <span class="stat__label">Stage Vision</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">Open</span>
                            <span class="stat__label">Source Future</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">Big</span>
                            <span class="stat__label">Picture Thinking</span>
                        </div>
                    </div>
                </div>

                <div class="hero__visual">
                    <div class="universal-wallet-demo">
                        <!-- This will be enhanced in Task 2 with sophisticated animations -->
                        <div class="wallet-core">
                            <i class="fas fa-wallet"></i>
                            <span>zkd.app</span>
                        </div>
                        <div class="chain-orbits">
                            <div class="chain-orbit chain-orbit--ethereum">
                                <i class="fab fa-ethereum"></i>
                            </div>
                            <div class="chain-orbit chain-orbit--solana">
                                <i class="fas fa-sun"></i>
                            </div>
                            <div class="chain-orbit chain-orbit--polygon">
                                <i class="fas fa-shapes"></i>
                            </div>
                            <div class="chain-orbit chain-orbit--arbitrum">
                                <i class="fas fa-layer-group"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Problem/Solution Section -->
        <section class="problem-solution" id="problem-solution">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">The Web3 UX Problem is Real</h2>
                    <p class="section__subtitle">
                        Web3 users juggle multiple wallets, remember countless seed phrases, and constantly switch networks.
                        We envision a future where this complexity disappears entirely.
                    </p>
                </div>

                <div class="comparison">
                    <div class="comparison__before">
                        <h3 class="comparison__title">
                            <i class="fas fa-times-circle"></i>
                            Current Web3 Reality
                        </h3>
                        <div class="pain-points">
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Multiple wallets for different chains</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Constant network switching</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Seed phrase management nightmare</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Popup approval fatigue</span>
                            </div>
                            <div class="pain-point">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Gas token juggling</span>
                            </div>
                        </div>
                    </div>

                    <div class="comparison__after">
                        <h3 class="comparison__title">
                            <i class="fas fa-check-circle"></i>
                            Our Vision
                        </h3>
                        <div class="benefits">
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>One universal smart account</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Automatic chain detection</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Biometric & social login</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Session-based permissions</span>
                            </div>
                            <div class="benefit">
                                <i class="fas fa-check"></i>
                                <span>Universal gas abstraction</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section class="how-it-works" id="how-it-works">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">The Universal Wallet Vision</h2>
                    <p class="section__subtitle">
                        How we envision the future of Web3 user experience. Simple, seamless, universal.
                    </p>
                </div>

                <div class="workflow">
                    <div class="workflow__step">
                        <div class="step__visual">
                            <div class="step__icon">
                                <i class="fas fa-download"></i>
                            </div>
                            <div class="step__number">01</div>
                        </div>
                        <div class="step__content">
                            <h3 class="step__title">Simple Onboarding</h3>
                            <p class="step__description">
                                One-time setup creates your universal smart account. No seed phrases,
                                no complex configurations. Just secure, simple authentication.
                            </p>
                            <div class="step__features">
                                <span class="feature-tag">Biometric Auth</span>
                                <span class="feature-tag">Social Login</span>
                                <span class="feature-tag">Smart Account</span>
                            </div>
                        </div>
                    </div>

                    <div class="workflow__step">
                        <div class="step__visual">
                            <div class="step__icon">
                                <i class="fas fa-plug"></i>
                            </div>
                            <div class="step__number">02</div>
                        </div>
                        <div class="step__content">
                            <h3 class="step__title">Universal Compatibility</h3>
                            <p class="step__description">
                                Works seamlessly with every dApp across all ecosystems. No integration
                                required from developers. True universal compatibility.
                            </p>
                            <div class="step__features">
                                <span class="feature-tag">Universal Provider</span>
                                <span class="feature-tag">Zero Integration</span>
                                <span class="feature-tag">Cross-Chain</span>
                            </div>
                        </div>
                    </div>

                    <div class="workflow__step">
                        <div class="step__visual">
                            <div class="step__icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div class="step__number">03</div>
                        </div>
                        <div class="step__content">
                            <h3 class="step__title">Frictionless Experience</h3>
                            <p class="step__description">
                                Set preferences once, transact everywhere. No popups, no network switching,
                                no gas token juggling. Just seamless Web3 interactions.
                            </p>
                            <div class="step__features">
                                <span class="feature-tag">Session Security</span>
                                <span class="feature-tag">Gas Abstraction</span>
                                <span class="feature-tag">Zero Friction</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Innovation Section with Tabs -->
        <section class="innovation" id="innovation">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Next-Generation Technology</h2>
                    <p class="section__subtitle">
                        The technical innovations that will make universal wallet functionality possible.
                        Deep research into blended execution and cross-VM compatibility.
                    </p>
                </div>

                <div class="innovation__tabs">
                    <div class="tab__nav">
                        <button class="tab__button tab__button--active" data-tab="fluent">
                            <i class="fas fa-cube"></i>
                            Blended Execution
                        </button>
                        <button class="tab__button" data-tab="jzkt">
                            <i class="fas fa-sitemap"></i>
                            JZKT Research
                        </button>
                        <button class="tab__button" data-tab="execution">
                            <i class="fas fa-network-wired"></i>
                            Universal Compatibility
                        </button>
                        <button class="tab__button" data-tab="security">
                            <i class="fas fa-shield-alt"></i>
                            Security Vision
                        </button>
                    </div>

                    <div class="tab__content">
                        <!-- Fluent L2 Tab -->
                        <div class="tab__panel tab__panel--active" id="fluent">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="fluent-diagram">
                                        <!-- Enhanced in Task 2 -->
                                        <div class="blended-execution">
                                            <div class="vm-layer vm-layer--evm">
                                                <i class="fab fa-ethereum"></i>
                                                <span>EVM</span>
                                            </div>
                                            <div class="vm-layer vm-layer--svm">
                                                <i class="fas fa-sun"></i>
                                                <span>SVM</span>
                                            </div>
                                            <div class="vm-layer vm-layer--wasm">
                                                <i class="fas fa-cogs"></i>
                                                <span>WASM</span>
                                            </div>
                                            <div class="shared-state">
                                                <span>Unified Shared State</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Blended Execution Foundation</h3>
                                    <p class="panel__description">
                                        The future of blockchain infrastructure supports simultaneous execution across
                                        multiple virtual machines in a unified environment. This breakthrough enables
                                        true universal wallet functionality for the first time.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Simultaneous multi-VM execution</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Unified shared state across all VMs</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Native compatibility contracts</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Zero-knowledge proof verification</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- JZKT Tab -->
                        <div class="tab__panel" id="jzkt">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="jzkt-diagram">
                                        <!-- Enhanced in Task 2 with sophisticated JZKT visualization -->
                                        <div class="trie-structure">
                                            <div class="trie-node trie-node--root">
                                                <span>rWasm VM</span>
                                            </div>
                                            <div class="trie-branches">
                                                <div class="trie-node trie-node--branch">
                                                    <span>EVM Compatibility</span>
                                                </div>
                                                <div class="trie-node trie-node--branch">
                                                    <span>SVM Compatibility</span>
                                                </div>
                                                <div class="trie-node trie-node--branch">
                                                    <span>WASM Compatibility</span>
                                                </div>
                                            </div>
                                            <div class="journal-layer">
                                                <span>Journaled ZK Trie (JZKT)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Journaled ZK Trie (JZKT)</h3>
                                    <p class="panel__description">
                                        JZKT is the foundational library that enables atomic composability and efficient
                                        ZK proving across Fluent's compatibility contracts. All VMs compile down to rWasm
                                        (reduced WebAssembly) for unified execution and zero-knowledge operations.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Reversible state transitions</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Efficient ZK proof generation</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Cross-VM state consistency</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Atomic rollback capabilities</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cross-VM Execution Tab -->
                        <div class="tab__panel" id="execution">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="execution-flow">
                                        <!-- Enhanced in Task 2 -->
                                        <div class="flow-step">
                                            <span>User Transaction</span>
                                        </div>
                                        <div class="flow-arrow">→</div>
                                        <div class="flow-step">
                                            <span>Compatibility Contracts</span>
                                        </div>
                                        <div class="flow-arrow">→</div>
                                        <div class="flow-step">
                                            <span>Atomic Execution</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Atomic Cross-VM Execution</h3>
                                    <p class="panel__description">
                                        Compatibility contracts enable atomic composability between different virtual
                                        machines. A single transaction can interact with Ethereum smart contracts,
                                        Solana programs, and WASM modules simultaneously.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Atomic cross-VM transactions</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Unified API across all VMs</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Automatic rollback on failure</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Real-time composability</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Model Tab -->
                        <div class="tab__panel" id="security">
                            <div class="panel__grid">
                                <div class="panel__visual">
                                    <div class="security-layers">
                                        <!-- Enhanced in Task 2 -->
                                        <div class="security-layer">
                                            <i class="fas fa-fingerprint"></i>
                                            <span>Biometric Auth</span>
                                        </div>
                                        <div class="security-layer">
                                            <i class="fas fa-key"></i>
                                            <span>Session Keys</span>
                                        </div>
                                        <div class="security-layer">
                                            <i class="fas fa-shield-alt"></i>
                                            <span>Smart Contract Security</span>
                                        </div>
                                        <div class="security-layer">
                                            <i class="fas fa-users"></i>
                                            <span>Social Recovery</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel__content">
                                    <h3 class="panel__title">Multi-Layer Security Model</h3>
                                    <p class="panel__description">
                                        zkd.app implements a comprehensive security model with biometric authentication,
                                        session-based permissions, smart contract security, and social recovery mechanisms.
                                    </p>
                                    <div class="panel__features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Hardware-backed biometric auth</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Configurable session limits</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Multi-signature capabilities</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i>
                                            <span>Decentralized social recovery</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Live Demo Section -->
        <section class="demo" id="demo">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">See zkd.app in Action</h2>
                    <p class="section__subtitle">
                        Watch how zkd.app transforms the Web3 experience with seamless cross-chain interactions.
                    </p>
                </div>

                <div class="demo__content">
                    <div class="demo__comparison">
                        <div class="comparison__side comparison__side--before">
                            <h3 class="comparison__title">
                                <i class="fas fa-times-circle"></i>
                                Traditional Wallet Experience
                            </h3>
                            <div class="demo__video">
                                <!-- Placeholder for demo video -->
                                <div class="video-placeholder">
                                    <i class="fas fa-play-circle"></i>
                                    <span>Traditional Wallet Demo</span>
                                </div>
                            </div>
                            <div class="demo__steps">
                                <div class="demo__step">
                                    <span class="step__number">1</span>
                                    <span>Install MetaMask + Phantom</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">2</span>
                                    <span>Switch networks manually</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">3</span>
                                    <span>Approve every transaction</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">4</span>
                                    <span>Manage multiple seed phrases</span>
                                </div>
                            </div>
                        </div>

                        <div class="comparison__side comparison__side--after">
                            <h3 class="comparison__title">
                                <i class="fas fa-check-circle"></i>
                                zkd.app Experience
                            </h3>
                            <div class="demo__video">
                                <!-- Placeholder for demo video -->
                                <div class="video-placeholder">
                                    <i class="fas fa-play-circle"></i>
                                    <span>zkd.app Demo</span>
                                </div>
                            </div>
                            <div class="demo__steps">
                                <div class="demo__step">
                                    <span class="step__number">1</span>
                                    <span>Install zkd.app extension</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">2</span>
                                    <span>Automatic chain detection</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">3</span>
                                    <span>Session-based permissions</span>
                                </div>
                                <div class="demo__step">
                                    <span class="step__number">4</span>
                                    <span>Biometric authentication</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="demo__cta">
                        <a href="#waitlist" class="btn btn--primary btn--large">
                            <i class="fas fa-rocket"></i>
                            Experience the Future - Join Waitlist
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Community Building Section -->
        <section class="community" id="community">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Building Together</h2>
                    <p class="section__subtitle">
                        This vision is bigger than any one team. We need builders, visionaries, and believers
                        to make universal wallets a reality.
                    </p>
                </div>

                <div class="community__content">
                    <div class="community__needs">
                        <div class="need-card">
                            <div class="need-card__icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="need-card__content">
                                <h3 class="need-card__title">Technical Contributors</h3>
                                <p class="need-card__description">
                                    Blockchain developers, smart contract engineers, and UX researchers
                                    to help build the future of Web3 wallets.
                                </p>
                            </div>
                        </div>
                        <div class="need-card">
                            <div class="need-card__icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="need-card__content">
                                <h3 class="need-card__title">Visionaries & Advisors</h3>
                                <p class="need-card__description">
                                    Web3 leaders, product strategists, and industry experts to guide
                                    the vision and ensure we're solving real problems.
                                </p>
                            </div>
                        </div>
                        <div class="need-card">
                            <div class="need-card__icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="need-card__content">
                                <h3 class="need-card__title">Partners & Supporters</h3>
                                <p class="need-card__description">
                                    dApp developers, infrastructure providers, and early believers
                                    who want to shape the future of Web3 UX.
                                </p>
                            </div>
                        </div>
                        <div class="need-card">
                            <div class="need-card__icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="need-card__content">
                                <h3 class="need-card__title">Early Supporters</h3>
                                <p class="need-card__description">
                                    Community members who believe in the vision and want to help
                                    validate, test, and spread the word about universal wallets.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="vision-quotes">
                        <div class="vision-quote">
                            <div class="quote__content">
                                <p>"Web3 UX is fundamentally broken. We need universal wallets that abstract away the complexity while maintaining security and decentralization."</p>
                            </div>
                            <div class="quote__context">
                                <span class="quote__label">The Problem</span>
                            </div>
                        </div>
                        <div class="vision-quote">
                            <div class="quote__content">
                                <p>"Blended execution environments like Fluent L2 make cross-VM compatibility possible. This is the foundation for true universal wallets."</p>
                            </div>
                            <div class="quote__context">
                                <span class="quote__label">The Technology</span>
                            </div>
                        </div>
                        <div class="vision-quote">
                            <div class="quote__content">
                                <p>"The future of Web3 is seamless. One account, every chain, zero friction. This vision requires collaboration across the entire ecosystem."</p>
                            </div>
                            <div class="quote__context">
                                <span class="quote__label">The Vision</span>
                            </div>
                        </div>
                    </div>

                    <div class="community__links">
                        <a href="#waitlist" class="community__link community__link--primary">
                            <i class="fas fa-users"></i>
                            <span>Join the Vision</span>
                        </a>
                        <a href="#" class="community__link">
                            <i class="fas fa-code"></i>
                            <span>Contribute Ideas</span>
                        </a>
                        <a href="#" class="community__link">
                            <i class="fas fa-handshake"></i>
                            <span>Partner With Us</span>
                        </a>
                        <a href="#" class="community__link">
                            <i class="fas fa-envelope"></i>
                            <span>Get Updates</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Early Supporters Section -->
        <section class="waitlist" id="waitlist">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Early Supporter Program</h2>
                    <p class="section__subtitle">
                        Join the vision from the beginning. Help shape the future of Web3 wallets and
                        be part of something revolutionary.
                    </p>
                </div>

                <div class="waitlist__content">
                    <div class="waitlist__tiers">
                        <div class="tier tier--alpha">
                            <div class="tier__header">
                                <h3 class="tier__title">Genesis Supporters</h3>
                                <div class="tier__badge">Founding</div>
                            </div>
                            <div class="tier__benefits">
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Genesis NFT with utility</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Governance voting rights</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Private Discord access</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Product development input</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Revenue sharing potential</span>
                                </div>
                            </div>
                            <div class="tier__spots">
                                <span class="spots__remaining">Building community</span>
                            </div>
                        </div>

                        <div class="tier tier--beta">
                            <div class="tier__header">
                                <h3 class="tier__title">Community Builders</h3>
                                <div class="tier__badge">Vision</div>
                            </div>
                            <div class="tier__benefits">
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Early access to updates</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Community token allocation</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Beta testing opportunities</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Feature feedback input</span>
                                </div>
                                <div class="benefit">
                                    <i class="fas fa-check"></i>
                                    <span>Community recognition</span>
                                </div>
                            </div>
                            <div class="tier__spots">
                                <span class="spots__remaining">Open to all believers</span>
                            </div>
                        </div>
                    </div>

                    <div class="waitlist__form">
                        <form class="signup-form">
                            <div class="form__group">
                                <input type="email" class="form__input" placeholder="Enter your email" required>
                                <select class="form__select">
                                    <option value="">I want to help with...</option>
                                    <option value="technical">Technical Development</option>
                                    <option value="design">UX/UI Design</option>
                                    <option value="community">Community Building</option>
                                    <option value="partnerships">Business Development</option>
                                    <option value="funding">Investment/Funding</option>
                                    <option value="testing">Early Testing</option>
                                    <option value="other">Other Support</option>
                                </select>
                                <button type="submit" class="btn btn--primary btn--waitlist">
                                    <i class="fas fa-users"></i>
                                    Join the Vision
                                </button>
                            </div>
                        </form>

                        <div class="referral__info">
                            <p>Help us build the future together. Every supporter brings us closer to universal Web3 UX.</p>
                        </div>
                    </div>

                    <div class="waitlist__stats">
                        <div class="stat">
                            <span class="stat__number">Early</span>
                            <span class="stat__label">Stage Project</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">Big</span>
                            <span class="stat__label">Vision Thinking</span>
                        </div>
                        <div class="stat">
                            <span class="stat__number">Open</span>
                            <span class="stat__label">To Collaboration</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="faq" id="faq">
            <div class="container">
                <div class="section__header">
                    <h2 class="section__title">Frequently Asked Questions</h2>
                    <p class="section__subtitle">
                        Everything you need to know about zkd.app and how it works.
                    </p>
                </div>

                <div class="faq__content">
                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>How is zkd.app different from MetaMask or Phantom?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>zkd.app is a universal smart account wallet that works across ALL chains with a single account. Unlike MetaMask (Ethereum-focused) or Phantom (Solana-focused), zkd.app provides native compatibility with EVM, SVM, and WASM environments through Fluent L2's blended execution network.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>Do dApps need to integrate with zkd.app?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>No! zkd.app works with existing dApps without any code changes. Our browser extension injects the appropriate provider (MetaMask, Phantom, etc.) that dApps expect, ensuring 100% compatibility with the current Web3 ecosystem.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>How secure is the session-based authentication?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>zkd.app uses hardware-backed biometric authentication combined with configurable session limits. You can set spending limits, time restrictions, and dApp permissions. All transactions are secured by smart contract logic and can include multi-signature requirements.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>What is Fluent L2's blended execution network?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>Fluent L2 is the first blockchain to support simultaneous execution across EVM, SVM, and WASM virtual machines in a unified shared state. This enables true cross-VM composability and atomic transactions across different blockchain ecosystems.</p>
                        </div>
                    </div>

                    <div class="faq__item">
                        <div class="faq__question">
                            <h3>When will zkd.app be available?</h3>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq__answer">
                            <p>We're currently in alpha testing with limited access. Alpha users get early access to the browser extension, exclusive benefits, and direct input on features. Beta launch is planned for Q2 2024, with public release following shortly after.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <!-- Footer Header with Key Metrics -->
            <div class="footer__header">
                <div class="footer__metrics">
                    <div class="footer__metric">
                        <div class="metric__value">Vision</div>
                        <div class="metric__label">Driven Development</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">Open</div>
                        <div class="metric__label">Source Future</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">Community</div>
                        <div class="metric__label">Built Together</div>
                    </div>
                    <div class="footer__metric">
                        <div class="metric__value">Universal</div>
                        <div class="metric__label">Web3 Access</div>
                    </div>
                </div>
            </div>

            <div class="footer__content">
                <div class="footer__brand">
                    <h3 class="footer__logo">zkd.app</h3>
                    <p class="footer__description">
                        Building the universal smart account wallet vision on next-generation <strong>blended execution technology</strong>.
                        Researching seamless cross-VM execution and atomic composability to create the future of Web3 UX.
                    </p>
                    <div class="footer__tech">
                        <span class="tech__badge tech__badge--primary">
                            <i class="fas fa-cube"></i>
                            Blended Execution
                        </span>
                        <span class="tech__badge tech__badge--secondary">
                            <i class="fab fa-rust"></i>
                            Rust Core
                        </span>
                        <span class="tech__badge tech__badge--accent">
                            <i class="fas fa-link"></i>
                            JZKT Architecture
                        </span>
                        <span class="tech__badge">
                            <i class="fas fa-shield-alt"></i>
                            Open Source
                        </span>
                    </div>
                    <div class="footer__cta">
                        <a href="#waitlist" class="btn btn--primary btn--footer">
                            <i class="fas fa-users"></i>
                            Join the Vision
                        </a>
                    </div>
                </div>

                <div class="footer__links">
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-cube"></i>
                            Product
                        </h4>
                        <a href="#how-it-works" class="footer__link">
                            <i class="fas fa-play-circle"></i>
                            How It Works
                        </a>
                        <a href="#innovation" class="footer__link">
                            <i class="fas fa-sitemap"></i>
                            Technical Innovation
                        </a>
                        <a href="#demo" class="footer__link">
                            <i class="fas fa-video"></i>
                            Live Demo
                        </a>
                        <a href="#waitlist" class="footer__link footer__link--highlight">
                            <i class="fas fa-users"></i>
                            Join Vision
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-code"></i>
                            Developers
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fab fa-github"></i>
                            GitHub Repository
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-book"></i>
                            Technical Documentation
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-plug"></i>
                            Provider Injection API
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-shield-alt"></i>
                            Security Audits
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-users"></i>
                            Community
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fab fa-discord"></i>
                            Discord Server
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-twitter"></i>
                            Twitter Updates
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-telegram"></i>
                            Telegram Group
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fab fa-medium"></i>
                            Technical Blog
                        </a>
                    </div>
                    <div class="footer__section">
                        <h4 class="footer__title">
                            <i class="fas fa-layer-group"></i>
                            Ecosystem
                        </h4>
                        <a href="#" class="footer__link">
                            <i class="fas fa-handshake"></i>
                            Research Partnerships
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-chart-line"></i>
                            Performance Metrics
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-road"></i>
                            Development Roadmap
                        </a>
                        <a href="#" class="footer__link">
                            <i class="fas fa-gavel"></i>
                            Legal & Privacy
                        </a>
                    </div>
                </div>
            </div>

            <div class="footer__bottom">
                <div class="footer__bottom-content">
                    <div class="footer__copyright-section">
                        <p class="footer__copyright">
                            &copy; 2024 zkd.app. All rights reserved.
                        </p>
                        <p class="footer__tagline">
                            Built with <i class="fas fa-heart" style="color: var(--accent-color);"></i> for the future of Web3 • Powered by Fluent L2's Blended Execution Network
                        </p>
                    </div>
                    <div class="footer__social">
                        <a href="#" class="footer__social-link footer__social-link--github" aria-label="GitHub Repository">
                            <i class="fab fa-github"></i>
                            <span class="social__tooltip">GitHub</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--twitter" aria-label="Twitter Updates">
                            <i class="fab fa-twitter"></i>
                            <span class="social__tooltip">Twitter</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--discord" aria-label="Discord Community">
                            <i class="fab fa-discord"></i>
                            <span class="social__tooltip">Discord</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--telegram" aria-label="Telegram Group">
                            <i class="fab fa-telegram"></i>
                            <span class="social__tooltip">Telegram</span>
                        </a>
                        <a href="#" class="footer__social-link footer__social-link--medium" aria-label="Technical Blog">
                            <i class="fab fa-medium"></i>
                            <span class="social__tooltip">Medium</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="script.js" defer></script>
</body>
</html>
