/* ===== OPTIMIZED JAVASCRIPT - PERFORMANCE FOCUSED ===== */

// Performance monitoring
const perfMonitor = {
  start: performance.now(),
  marks: {},
  
  mark(name) {
    this.marks[name] = performance.now();
  },
  
  measure(name, startMark) {
    const duration = this.marks[name] - (this.marks[startMark] || this.start);
    console.log(`${name}: ${duration.toFixed(2)}ms`);
    return duration;
  }
};

// Debounce utility for performance
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Throttle utility for scroll events
function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}

// Optimized DOM ready
function domReady(fn) {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fn);
  } else {
    fn();
  }
}

// Lazy loading utility
class LazyLoader {
  constructor() {
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      { rootMargin: '50px 0px', threshold: 0.01 }
    );
  }

  observe(element) {
    this.observer.observe(element);
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const element = entry.target;
        
        // Load images
        if (element.dataset.src) {
          element.src = element.dataset.src;
          element.removeAttribute('data-src');
        }
        
        // Load background images
        if (element.dataset.bg) {
          element.style.backgroundImage = `url(${element.dataset.bg})`;
          element.removeAttribute('data-bg');
        }
        
        // Trigger animations
        if (element.dataset.animate) {
          element.classList.add('animate-in');
        }
        
        this.observer.unobserve(element);
      }
    });
  }
}

// Initialize lazy loader
const lazyLoader = new LazyLoader();

// Optimized mobile navigation
class MobileNav {
  constructor() {
    this.toggle = document.getElementById('nav-toggle');
    this.menu = document.getElementById('nav-menu');
    this.links = document.querySelectorAll('.nav__link');
    this.isOpen = false;
    
    this.init();
  }
  
  init() {
    if (!this.toggle || !this.menu) return;
    
    this.toggle.addEventListener('click', this.toggleMenu.bind(this));
    this.links.forEach(link => {
      link.addEventListener('click', this.closeMenu.bind(this));
    });
    
    // Close on outside click
    document.addEventListener('click', this.handleOutsideClick.bind(this));
    
    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.closeMenu();
      }
    });
  }
  
  toggleMenu() {
    this.isOpen = !this.isOpen;
    this.menu.classList.toggle('show-menu', this.isOpen);
    this.updateIcon();
    
    // Prevent body scroll when menu is open
    document.body.style.overflow = this.isOpen ? 'hidden' : '';
    
    // Accessibility
    this.toggle.setAttribute('aria-expanded', this.isOpen);
    this.menu.setAttribute('aria-hidden', !this.isOpen);
  }
  
  closeMenu() {
    this.isOpen = false;
    this.menu.classList.remove('show-menu');
    this.updateIcon();
    document.body.style.overflow = '';
    
    this.toggle.setAttribute('aria-expanded', 'false');
    this.menu.setAttribute('aria-hidden', 'true');
  }
  
  updateIcon() {
    const icon = this.toggle.querySelector('i');
    if (icon) {
      icon.className = this.isOpen ? 'fas fa-times' : 'fas fa-bars';
    }
  }
  
  handleOutsideClick(event) {
    if (!this.isOpen) return;
    
    const isClickInsideNav = this.menu.contains(event.target);
    const isClickOnToggle = this.toggle.contains(event.target);
    
    if (!isClickInsideNav && !isClickOnToggle) {
      this.closeMenu();
    }
  }
}

// Optimized smooth scrolling
class SmoothScroll {
  constructor() {
    this.headerHeight = 80; // Fixed header height for performance
    this.init();
  }
  
  init() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', this.handleClick.bind(this));
    });
  }
  
  handleClick(e) {
    e.preventDefault();
    const href = e.currentTarget.getAttribute('href');
    const target = document.querySelector(href);
    
    if (!target) return;
    
    const targetPosition = target.offsetTop - this.headerHeight;
    
    // Use native smooth scroll for better performance
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
    
    // Update URL without triggering scroll
    if (history.pushState) {
      history.pushState(null, null, href);
    }
  }
}

// Optimized form handling with validation
class FormHandler {
  constructor(formId) {
    this.form = document.getElementById(formId);
    this.isSubmitting = false;
    
    if (this.form) {
      this.init();
    }
  }
  
  init() {
    this.form.addEventListener('submit', this.handleSubmit.bind(this));
    
    // Real-time validation
    this.form.querySelectorAll('input, select').forEach(field => {
      field.addEventListener('blur', () => this.validateField(field));
      field.addEventListener('input', debounce(() => this.validateField(field), 300));
    });
  }
  
  async handleSubmit(e) {
    e.preventDefault();
    
    if (this.isSubmitting) return;
    
    const formData = new FormData(this.form);
    const data = Object.fromEntries(formData.entries());
    
    // Validate all fields
    const isValid = this.validateForm(data);
    if (!isValid) return;
    
    this.isSubmitting = true;
    this.showLoadingState();
    
    try {
      // Simulate API call (replace with actual endpoint)
      await this.submitToAPI(data);
      this.showSuccess();
      this.form.reset();
      this.updateStats();
    } catch (error) {
      this.showError(error.message);
    } finally {
      this.isSubmitting = false;
      this.hideLoadingState();
    }
  }
  
  validateForm(data) {
    let isValid = true;
    
    // Email validation
    if (!data.email || !this.isValidEmail(data.email)) {
      this.showFieldError('email', 'Please enter a valid email address');
      isValid = false;
    }
    
    // Required fields
    const requiredFields = ['role', 'experience', 'interest'];
    requiredFields.forEach(field => {
      if (!data[field]) {
        this.showFieldError(field, 'This field is required');
        isValid = false;
      }
    });
    
    return isValid;
  }
  
  validateField(field) {
    const value = field.value.trim();
    const name = field.name;
    
    // Clear previous errors
    this.clearFieldError(name);
    
    if (name === 'email' && value) {
      if (!this.isValidEmail(value)) {
        this.showFieldError(name, 'Please enter a valid email address');
        return false;
      }
    }
    
    if (field.required && !value) {
      this.showFieldError(name, 'This field is required');
      return false;
    }
    
    return true;
  }
  
  isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }
  
  showFieldError(fieldName, message) {
    const field = this.form.querySelector(`[name="${fieldName}"]`);
    if (!field) return;
    
    field.classList.add('error');
    
    let errorElement = field.parentNode.querySelector('.field-error');
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'field-error';
      field.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
  }
  
  clearFieldError(fieldName) {
    const field = this.form.querySelector(`[name="${fieldName}"]`);
    if (!field) return;
    
    field.classList.remove('error');
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
      errorElement.remove();
    }
  }
  
  showLoadingState() {
    const submitBtn = this.form.querySelector('[type="submit"]');
    if (submitBtn) {
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
    }
  }
  
  hideLoadingState() {
    const submitBtn = this.form.querySelector('[type="submit"]');
    if (submitBtn) {
      submitBtn.disabled = false;
      submitBtn.innerHTML = '<i class="fas fa-rocket"></i> Secure My Early Access';
    }
  }
  
  async submitToAPI(data) {
    // Replace with actual API endpoint
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90% success rate for demo
          resolve({ success: true });
        } else {
          reject(new Error('Network error. Please try again.'));
        }
      }, 1500);
    });
  }
  
  showSuccess() {
    this.showNotification('🎉 Welcome to zkd.app early access! Check your email for next steps.', 'success');
  }
  
  showError(message) {
    this.showNotification(message, 'error');
  }
  
  showNotification(message, type) {
    // Use the existing notification system
    if (typeof showNotification === 'function') {
      showNotification(message, type);
    }
  }
  
  updateStats() {
    // Update remaining spots counter
    const remainingSpots = document.querySelector('[data-target="847"]');
    if (remainingSpots) {
      const currentValue = parseInt(remainingSpots.textContent.replace(/,/g, ''));
      const newValue = Math.max(0, currentValue - 1);
      remainingSpots.textContent = newValue.toLocaleString();
    }
  }
}

// Initialize everything when DOM is ready
domReady(() => {
  perfMonitor.mark('init-start');
  
  // Initialize components
  new MobileNav();
  new SmoothScroll();
  new FormHandler('early-access-form');
  
  // Initialize lazy loading for images and animations
  document.querySelectorAll('[data-src], [data-bg], [data-animate]').forEach(el => {
    lazyLoader.observe(el);
  });
  
  perfMonitor.mark('init-end');
  perfMonitor.measure('init-end', 'init-start');
});
