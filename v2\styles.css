/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #00D4FF;
  --secondary-color: #8B5CF6;
  --accent-color: #10B981;
  --background-dark: #0A0A0A;
  --background-section: #111111;
  --background-card: #1A1A1A;
  --text-color: #FFFFFF;
  --text-light: #B3B3B3;
  --text-muted: #666666;
  --white: #FFFFFF;
  --gray-200: rgba(255, 255, 255, 0.1);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;

  /* Border Radius */
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  --border-radius-xl: 1.5rem;

  /* Transitions */
  --transition: all 0.3s ease;
  --transition-fast: all 0.15s ease;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-dark);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: inherit;
  text-decoration: none;
  transition: var(--transition);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--background-dark);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: var(--z-tooltip);
}

.skip-link:focus {
  top: 6px;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.btn--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.btn--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn--secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--primary-color);
}

.btn--large {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-lg);
}

.btn--hero {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--font-size-lg);
}

/* ===== HEADER & NAVIGATION ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: var(--z-fixed);
  transition: var(--transition);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
}

.nav__brand {
  display: flex;
  align-items: center;
}

.nav__logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.logo__icon {
  font-size: var(--font-size-2xl);
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo__text {
  display: flex;
  flex-direction: column;
  line-height: 1.1;
}

.logo__main {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo__sub {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
}

.nav__list {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  list-style: none;
}

.nav__link {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  transition: var(--transition);
  position: relative;
}

.nav__link:hover {
  color: var(--primary-color);
}

.nav__link--cta {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
}

.nav__link--cta:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.nav__toggle {
  display: none;
  font-size: var(--font-size-xl);
  color: var(--text-color);
  cursor: pointer;
}

/* ===== MAIN CONTENT ===== */
.main {
  padding-top: 80px; /* Account for fixed header */
}

/* ===== SECTIONS ===== */
section {
  padding: var(--spacing-4xl) 0;
  position: relative;
}

.section__header {
  text-align: center;
  margin-bottom: var(--spacing-4xl);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.section__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, var(--text-color), var(--text-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section__subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-light);
  line-height: 1.7;
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%),
              var(--background-dark);
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.02)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero__container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero__content {
  animation: slideInLeft 1s ease-out;
}

.hero__badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: var(--border-radius-xl);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xl);
  backdrop-filter: blur(10px);
}

.hero__title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  line-height: 1.1;
  margin-bottom: var(--spacing-xl);
  animation: slideInUp 1s ease-out 0.2s both;
}

.hero__description {
  font-size: var(--font-size-lg);
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: var(--spacing-2xl);
  animation: slideInUp 1s ease-out 0.4s both;
}

.hero__features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-2xl);
}

.feature-highlight {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  transition: var(--transition);
  animation: slideInUp 1s ease-out calc(0.6s + var(--delay, 0s)) both;
}

.feature-highlight:nth-child(1) { --delay: 0s; }
.feature-highlight:nth-child(2) { --delay: 0.1s; }
.feature-highlight:nth-child(3) { --delay: 0.2s; }

.feature-highlight:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
  transform: translateX(5px);
}

.feature-highlight i {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  min-width: 24px;
}

.hero__cta {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
  animation: slideInUp 1s ease-out 1s both;
}

.hero__stats {
  display: flex;
  gap: var(--spacing-2xl);
  animation: slideInUp 1s ease-out 1.2s both;
}

.stat {
  text-align: center;
}

.stat__number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat__label {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
}

/* ===== HERO VISUAL - UNIVERSAL WALLET DEMO ===== */
.hero__visual {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: slideInRight 1s ease-out 0.5s both;
}

.universal-wallet-demo {
  position: relative;
  width: 350px;
  height: 350px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.wallet-core {
  position: relative;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--background-dark);
  font-weight: var(--font-weight-bold);
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
  animation: float 6s ease-in-out infinite;
  z-index: 10;
}

.wallet-core i {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-xs);
}

.wallet-core span {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-extrabold);
}

.chain-orbits {
  position: absolute;
  width: 100%;
  height: 100%;
}

.chain-orbit {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-lg);
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

.chain-orbit--ethereum {
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: #627EEA;
  border-color: #627EEA;
  animation: orbit1 20s linear infinite;
}

.chain-orbit--solana {
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  color: #9945FF;
  border-color: #9945FF;
  animation: orbit2 25s linear infinite;
}

.chain-orbit--polygon {
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: #8247E5;
  border-color: #8247E5;
  animation: orbit3 30s linear infinite;
}

.chain-orbit--arbitrum {
  top: 50%;
  left: 30px;
  transform: translateY(-50%);
  color: #28A0F0;
  border-color: #28A0F0;
  animation: orbit4 35s linear infinite;
}

.chain-orbit:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px currentColor;
}

/* ===== KEYFRAME ANIMATIONS ===== */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

@keyframes orbit1 {
  from { transform: translateX(-50%) rotate(0deg) translateX(120px) rotate(0deg); }
  to { transform: translateX(-50%) rotate(360deg) translateX(120px) rotate(-360deg); }
}

@keyframes orbit2 {
  from { transform: translateY(-50%) rotate(0deg) translateX(120px) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg) translateX(120px) rotate(-360deg); }
}

@keyframes orbit3 {
  from { transform: translateX(-50%) rotate(0deg) translateX(120px) rotate(0deg); }
  to { transform: translateX(-50%) rotate(360deg) translateX(120px) rotate(-360deg); }
}

@keyframes orbit4 {
  from { transform: translateY(-50%) rotate(0deg) translateX(120px) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg) translateX(120px) rotate(-360deg); }
}

/* ===== PROBLEM/SOLUTION SECTION ===== */
.problem-solution {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(0, 212, 255, 0.05) 100%);
  position: relative;
}

.problem-solution::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="60" height="60" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  position: relative;
  z-index: 2;
}

.comparison::before {
  content: 'VS';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--primary-color);
  background: var(--background-dark);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 50%;
  border: 3px solid var(--primary-color);
  z-index: 10;
  animation: pulse 2s ease-in-out infinite;
}

.comparison__before,
.comparison__after {
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  position: relative;
  transition: var(--transition);
}

.comparison__before {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.2);
}

.comparison__before:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(255, 0, 0, 0.2);
}

.comparison__after {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid rgba(0, 255, 0, 0.2);
}

.comparison__after:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 255, 0, 0.2);
}

.comparison__title {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xl);
}

.comparison__title i {
  font-size: var(--font-size-2xl);
}

.comparison__before .comparison__title {
  color: #FF6B6B;
}

.comparison__after .comparison__title {
  color: var(--accent-color);
}

.pain-points,
.benefits {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.pain-point,
.benefit {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.pain-point {
  background: rgba(255, 107, 107, 0.1);
  border-left: 4px solid #FF6B6B;
}

.pain-point:hover {
  background: rgba(255, 107, 107, 0.2);
  transform: translateX(10px);
}

.pain-point i {
  color: #FF6B6B;
  font-size: var(--font-size-lg);
}

.benefit {
  background: rgba(16, 185, 129, 0.1);
  border-left: 4px solid var(--accent-color);
}

.benefit:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: translateX(10px);
}

.benefit i {
  color: var(--accent-color);
  font-size: var(--font-size-lg);
}

/* ===== HOW IT WORKS SECTION ===== */
.how-it-works {
  background: var(--background-section);
  position: relative;
}

.workflow {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4xl);
  position: relative;
}

.workflow::before {
  content: '';
  position: absolute;
  top: 60px;
  left: 16.66%;
  right: 16.66%;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
  z-index: 1;
}

.workflow__step {
  position: relative;
  text-align: center;
  z-index: 2;
}

.step__visual {
  position: relative;
  margin-bottom: var(--spacing-xl);
  display: flex;
  justify-content: center;
  align-items: center;
}

.step__icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-3xl);
  color: var(--background-dark);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
  transition: var(--transition);
  position: relative;
  z-index: 3;
}

.step__icon:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 35px rgba(0, 212, 255, 0.4);
}

.step__number {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 40px;
  height: 40px;
  background: var(--accent-color);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--background-dark);
  border: 3px solid var(--background-section);
}

.step__content {
  max-width: 300px;
  margin: 0 auto;
}

.step__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.step__description {
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.step__features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
}

.feature-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  transition: var(--transition);
}

.feature-tag:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
}

/* ===== TECHNICAL INNOVATION SECTION ===== */
.innovation {
  background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.1) 0%, transparent 70%),
              var(--background-dark);
  position: relative;
}

.innovation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="circuit" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M0 25h25v-25h25v50h-25v-25h-25z" fill="none" stroke="rgba(139,92,246,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23circuit)"/></svg>');
  opacity: 0.3;
}

.innovation__tabs {
  position: relative;
  z-index: 2;
}

.tab__nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-4xl);
  flex-wrap: wrap;
}

.tab__button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg) var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  color: var(--text-light);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.tab__button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.tab__button:hover::before {
  left: 100%;
}

.tab__button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.tab__button--active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-color: var(--primary-color);
  color: var(--background-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.tab__button i {
  font-size: var(--font-size-lg);
}

.tab__content {
  position: relative;
}

.tab__panel {
  display: none;
  animation: fadeInUp 0.5s ease-out;
}

.tab__panel--active {
  display: block;
}

.panel__grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
}

.panel__visual {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.panel__content {
  max-width: 500px;
}

.panel__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.panel__description {
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.panel__features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(10px);
}

.feature-item i {
  color: var(--accent-color);
  font-size: var(--font-size-lg);
  min-width: 20px;
}

/* ===== FLUENT L2 DIAGRAM ===== */
.fluent-diagram {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.blended-execution {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  overflow: hidden;
  min-height: 300px;
}

.vm-layer {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

/* Removed excessive VM layer shimmer effects */

.vm-layer--evm {
  background: rgba(98, 126, 234, 0.2);
  border: 1px solid #627EEA;
  color: #627EEA;
}

.vm-layer--svm {
  background: rgba(153, 69, 255, 0.2);
  border: 1px solid #9945FF;
  color: #9945FF;
}

.vm-layer--wasm {
  background: rgba(255, 165, 0, 0.2);
  border: 1px solid #FFA500;
  color: #FFA500;
}

.vm-layer i {
  font-size: var(--font-size-xl);
  position: relative;
  z-index: 2;
}

.vm-layer span {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  position: relative;
  z-index: 2;
}

.shared-state {
  text-align: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-bold);
  margin-top: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

/* Removed excessive shimmer - replaced with subtle static gradient */

/* Removed excessive shimmer animation */

/* ===== JZKT DIAGRAM ===== */
.jzkt-diagram {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.trie-structure {
  position: relative;
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  overflow: hidden;
  min-height: 300px;
}

.trie-node {
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
  color: var(--background-dark);
  border-radius: var(--border-radius-lg);
  text-align: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  position: relative;
  transition: var(--transition);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.trie-node:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.5);
}

.trie-node--root {
  margin-bottom: var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  font-size: var(--font-size-base);
}

.trie-branches {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  position: relative;
}

/* Removed trie data flow animation - keeping it simple and professional */

.trie-branches::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 20px;
  background: linear-gradient(180deg, var(--primary-color), transparent);
  z-index: 1;
}

.trie-branches .trie-node--branch {
  position: relative;
  z-index: 2;
}

.trie-branches .trie-node--branch::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 20px;
  background: linear-gradient(180deg, transparent, var(--secondary-color));
  z-index: 1;
}

.trie-branches .trie-node--branch:nth-child(1) {
  background: linear-gradient(135deg, #627EEA, #4A90E2);
}

.trie-branches .trie-node--branch:nth-child(2) {
  background: linear-gradient(135deg, #9945FF, #7B2CBF);
}

.trie-branches .trie-node--branch:nth-child(3) {
  background: linear-gradient(135deg, #FFA500, #FF8C00);
}

.journal-layer {
  text-align: center;
  padding: var(--spacing-lg);
  background: rgba(16, 185, 129, 0.2);
  border: 2px dashed var(--accent-color);
  border-radius: var(--border-radius-lg);
  color: var(--accent-color);
  font-weight: var(--font-weight-bold);
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Completely Remove Problematic Journal Animation */
/* The journal layer will be static with subtle styling only */

/* ===== EXECUTION FLOW DIAGRAM ===== */
.execution-flow {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  flex-wrap: nowrap;
  justify-content: center;
  overflow: hidden;
  min-height: 120px;
}

.flow-step {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-bold);
  text-align: center;
  min-width: 120px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  z-index: 2;
}

/* Removed excessive flow step shimmer effects */

.flow-step:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.flow-arrow {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
  opacity: 0.8;
}

/* Removed distracting arrow pulse animation */

/* ===== SECURITY LAYERS DIAGRAM ===== */
.security-layers {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  overflow: hidden;
  min-height: 200px;
}

.security-layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  transition: var(--transition);
  text-align: center;
}

.security-layer:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.security-layer i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.security-layer span {
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
}

.security-layer:nth-child(1) i { color: #FF6B6B; }
.security-layer:nth-child(2) i { color: var(--primary-color); }
.security-layer:nth-child(3) i { color: var(--accent-color); }
.security-layer:nth-child(4) i { color: var(--secondary-color); }

/* ===== FADE IN ANIMATION ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== DEMO SECTION ===== */
.demo {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
  position: relative;
}

.demo__comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  margin-bottom: var(--spacing-4xl);
}

.comparison__side {
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  transition: var(--transition);
}

.comparison__side--before {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.2);
}

.comparison__side--after {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.comparison__side:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.demo__video {
  margin-bottom: var(--spacing-xl);
}

.video-placeholder {
  aspect-ratio: 16/9;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-lg);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--text-light);
  transition: var(--transition);
  cursor: pointer;
}

.video-placeholder:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
}

.video-placeholder i {
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
}

.demo__steps {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.demo__step {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.demo__step:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(10px);
}

.demo__step .step__number {
  width: 30px;
  height: 30px;
  background: var(--primary-color);
  color: var(--background-dark);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
}

.demo__cta {
  text-align: center;
}

/* ===== COMMUNITY SECTION ===== */
.community {
  background: var(--background-section);
  position: relative;
}

/* Community Needs Section */
.community__needs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-4xl);
}

.need-card {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  transition: var(--transition);
}

.need-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.need-card__icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-xl);
  color: var(--background-dark);
  margin-bottom: var(--spacing-lg);
}

.need-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
}

.need-card__description {
  color: var(--text-light);
  line-height: 1.7;
  margin: 0;
}

/* Vision Quotes */
.vision-quotes {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-4xl);
}

.vision-quote {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  transition: var(--transition);
  text-align: center;
}

.vision-quote:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.quote__content {
  margin-bottom: var(--spacing-lg);
}

.quote__content p {
  color: var(--text-light);
  line-height: 1.7;
  font-style: italic;
  position: relative;
  font-size: var(--font-size-lg);
}

.quote__content p::before {
  content: '"';
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  position: absolute;
  top: -10px;
  left: -20px;
  font-family: serif;
}

.quote__context {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--background-dark);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm);
  display: inline-block;
}

.community__stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-4xl);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  transition: var(--transition);
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.stat-card__icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-xl);
  color: var(--background-dark);
}

.stat-card__content {
  flex: 1;
}

.stat-card__number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-card__label {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.testimonials {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-4xl);
}

.testimonial {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  transition: var(--transition);
}

.testimonial:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.testimonial__content {
  margin-bottom: var(--spacing-xl);
}

.testimonial__content p {
  color: var(--text-light);
  line-height: 1.7;
  font-style: italic;
  position: relative;
}

.testimonial__content p::before {
  content: '"';
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  position: absolute;
  top: -10px;
  left: -20px;
  font-family: serif;
}

.testimonial__author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author__avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--background-dark);
  font-size: var(--font-size-lg);
}

.author__info {
  flex: 1;
}

.author__name {
  display: block;
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.author__title {
  color: var(--text-light);
  font-size: var(--font-size-sm);
}

.community__links {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.community__link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  color: var(--text-light);
  font-weight: var(--font-weight-medium);
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.community__link:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.community__link--primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-color: var(--primary-color);
  color: var(--background-dark);
  font-weight: var(--font-weight-bold);
}

.community__link--primary:hover {
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
  color: var(--background-dark);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

/* ===== WAITLIST SECTION ===== */
.waitlist {
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%),
              var(--background-dark);
  position: relative;
}

.waitlist__tiers {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  margin-bottom: var(--spacing-4xl);
}

.tier {
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.tier::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.tier:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.tier--alpha::before {
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.tier--beta::before {
  background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
}

.tier__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.tier__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.tier__badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-color);
  color: var(--background-dark);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
}

.tier__benefits {
  margin-bottom: var(--spacing-xl);
}

.tier__benefits .benefit {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tier__benefits .benefit:last-child {
  border-bottom: none;
}

.tier__spots {
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: var(--border-radius);
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

.waitlist__form {
  max-width: 600px;
  margin: 0 auto var(--spacing-4xl);
  padding: var(--spacing-2xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(15px);
}

.signup-form .form__group {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.form__input,
.form__select {
  flex: 1;
  min-width: 200px;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: var(--font-size-base);
  transition: var(--transition);
}

.form__input:focus,
.form__select:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form__input::placeholder {
  color: var(--text-muted);
}

.btn--waitlist {
  min-width: 200px;
}

.referral__info {
  text-align: center;
  color: var(--text-light);
  font-size: var(--font-size-sm);
  padding: var(--spacing-md);
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: var(--border-radius);
}

.waitlist__stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4xl);
}

/* ===== FAQ SECTION ===== */
.faq {
  background: var(--background-section);
  position: relative;
}

.faq__content {
  max-width: 800px;
  margin: 0 auto;
}

.faq__item {
  margin-bottom: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(15px);
  overflow: hidden;
  transition: var(--transition);
}

.faq__item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.faq__item--active {
  border-color: var(--primary-color);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
}

.faq__question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl);
  cursor: pointer;
  transition: var(--transition);
}

.faq__question:hover {
  background: rgba(255, 255, 255, 0.05);
}

.faq__question h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin: 0;
}

.faq__question i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  transition: var(--transition);
}

.faq__answer {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
  color: var(--text-light);
  line-height: 1.7;
  display: none;
}

.faq__item--active .faq__answer {
  display: block;
  animation: fadeInUp 0.3s ease-out;
}

/* ===== FOOTER ===== */
.footer {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%),
              var(--background-dark);
  position: relative;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.footer__header {
  padding: var(--spacing-4xl) 0 var(--spacing-2xl);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer__metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
}

.footer__metric {
  text-align: center;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(15px);
  transition: var(--transition);
}

.footer__metric:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.metric__value {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.metric__label {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-4xl);
  padding: var(--spacing-4xl) 0;
}

.footer__brand {
  max-width: 400px;
}

.footer__logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-extrabold);
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.footer__logo::before {
  content: '⚡';
  font-size: var(--font-size-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer__description {
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-base);
}

.footer__tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.tech__badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-xs);
  color: var(--text-light);
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

.tech__badge:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.tech__badge i {
  color: var(--primary-color);
  transition: var(--transition);
}

.tech__badge--primary {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
  color: var(--primary-color);
}

.tech__badge--secondary {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.3);
  color: var(--secondary-color);
}

.tech__badge--secondary i {
  color: var(--secondary-color);
}

.tech__badge--accent {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
  color: var(--accent-color);
}

.tech__badge--accent i {
  color: var(--accent-color);
}

.footer__cta {
  margin-top: var(--spacing-lg);
}

.btn--footer {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
}

.btn--footer:hover {
  box-shadow: 0 6px 25px rgba(0, 212, 255, 0.5);
  transform: translateY(-3px);
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-2xl);
}

.footer__section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer__title i {
  color: var(--primary-color);
  font-size: var(--font-size-base);
}

.footer__link {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
  border-radius: var(--border-radius);
  position: relative;
}

.footer__link i {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  width: 16px;
  transition: var(--transition);
}

.footer__link:hover {
  color: var(--primary-color);
  padding-left: var(--spacing-sm);
}

.footer__link:hover i {
  color: var(--primary-color);
  transform: translateX(2px);
}

.footer__link--highlight {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.footer__link--highlight:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.footer__bottom {
  padding-top: var(--spacing-2xl);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: var(--spacing-2xl);
}

.footer__bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.footer__copyright-section {
  flex: 1;
  text-align: left;
}

.footer__copyright {
  color: var(--text-light);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.footer__tagline {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.footer__social {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.footer__social-link {
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  transition: var(--transition);
  position: relative;
  backdrop-filter: blur(10px);
}

.footer__social-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.footer__social-link--github:hover {
  background: #333;
  border-color: #333;
  color: white;
  box-shadow: 0 8px 25px rgba(51, 51, 51, 0.4);
}

.footer__social-link--twitter:hover {
  background: #1DA1F2;
  border-color: #1DA1F2;
  color: white;
  box-shadow: 0 8px 25px rgba(29, 161, 242, 0.4);
}

.footer__social-link--discord:hover {
  background: #5865F2;
  border-color: #5865F2;
  color: white;
  box-shadow: 0 8px 25px rgba(88, 101, 242, 0.4);
}

.footer__social-link--telegram:hover {
  background: #0088cc;
  border-color: #0088cc;
  color: white;
  box-shadow: 0 8px 25px rgba(0, 136, 204, 0.4);
}

.footer__social-link--medium:hover {
  background: #00ab6c;
  border-color: #00ab6c;
  color: white;
  box-shadow: 0 8px 25px rgba(0, 171, 108, 0.4);
}

.social__tooltip {
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--background-dark);
  color: var(--text-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer__social-link:hover .social__tooltip {
  opacity: 1;
  bottom: 110%;
}

/* ===== SCROLL ANIMATIONS ===== */
.animate-in {
  animation: slideInUp 0.6s ease-out;
}

/* ===== NOTIFICATION SYSTEM ===== */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: var(--spacing-md) var(--spacing-lg);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-lg);
  color: var(--text-color);
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transform: translateX(100%);
  transition: var(--transition);
  z-index: var(--z-tooltip);
}

.notification--show {
  transform: translateX(0);
}

.notification--success {
  border-color: var(--accent-color);
  background: rgba(16, 185, 129, 0.1);
}

.notification--success i {
  color: var(--accent-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  /* Tablet Styles */
  .container {
    padding: 0 var(--spacing-md);
  }

  .hero__container {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero__visual {
    order: -1;
  }

  .universal-wallet-demo {
    width: 300px;
    height: 300px;
  }

  .wallet-core {
    width: 100px;
    height: 100px;
  }

  .chain-orbit {
    width: 60px;
    height: 60px;
    font-size: var(--font-size-lg);
  }

  .comparison {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .comparison::before {
    top: 50%;
    left: -20px;
    transform: translateY(-50%) rotate(90deg);
  }

  .workflow {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .workflow::before {
    display: none;
  }

  .panel__grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  /* Technical Diagrams Responsive */
  .fluent-diagram,
  .jzkt-diagram,
  .execution-flow,
  .security-layers {
    max-width: 100%;
    padding: var(--spacing-lg);
  }

  .trie-structure {
    padding: var(--spacing-lg);
    min-height: 250px;
  }

  .execution-flow {
    flex-wrap: wrap;
    gap: var(--spacing-md);
    min-height: auto;
  }

  .security-layers {
    grid-template-columns: 1fr;
    min-height: auto;
  }

  .demo__comparison {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .community__needs {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .vision-quotes {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .community__stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .testimonials {
    grid-template-columns: 1fr;
  }

  .waitlist__tiers {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .footer__metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer__content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .footer__links {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  /* Mobile Styles */
  .container {
    padding: 0 var(--spacing-sm);
  }

  /* Header */
  .nav__menu {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: rgba(10, 10, 10, 0.98);
    backdrop-filter: blur(20px);
    transition: var(--transition);
    z-index: var(--z-fixed);
  }

  .nav__menu--active {
    left: 0;
  }

  .nav__list {
    flex-direction: column;
    gap: var(--spacing-2xl);
    padding: var(--spacing-4xl) var(--spacing-lg);
    height: 100%;
    justify-content: center;
  }

  .nav__toggle {
    display: block;
  }

  .nav__toggle--active i::before {
    content: '\f00d'; /* Font Awesome times icon */
  }

  /* Hero */
  .hero {
    min-height: 80vh;
    padding: var(--spacing-2xl) 0;
  }

  .hero__title {
    font-size: var(--font-size-3xl);
  }

  .hero__description {
    font-size: var(--font-size-base);
  }

  .hero__cta {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .hero__stats {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .universal-wallet-demo {
    width: 250px;
    height: 250px;
  }

  .wallet-core {
    width: 80px;
    height: 80px;
  }

  .chain-orbit {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-base);
  }

  /* Sections */
  section {
    padding: var(--spacing-2xl) 0;
  }

  .section__title {
    font-size: var(--font-size-2xl);
  }

  .section__subtitle {
    font-size: var(--font-size-base);
  }

  /* Tabs */
  .tab__nav {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .tab__button {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-xs);
  }

  /* Technical Diagrams Mobile */
  .fluent-diagram,
  .jzkt-diagram,
  .execution-flow,
  .security-layers {
    padding: var(--spacing-md);
  }

  .trie-structure {
    padding: var(--spacing-md);
    min-height: 200px;
  }

  .trie-branches {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .execution-flow {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .flow-step {
    min-width: auto;
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }

  .flow-arrow {
    transform: rotate(90deg);
    font-size: var(--font-size-lg);
  }

  /* Community */
  .community__needs {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .vision-quotes {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .community__stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .community__links {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  /* Waitlist */
  .signup-form .form__group {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .form__input,
  .form__select {
    min-width: auto;
    width: 100%;
  }

  .waitlist__stats {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  /* Footer */
  .footer__metrics {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .footer__metric {
    padding: var(--spacing-md);
  }

  .metric__value {
    font-size: var(--font-size-xl);
  }

  .footer__links {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .footer__bottom-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .footer__copyright-section {
    text-align: center;
  }

  .footer__tagline {
    justify-content: center;
    flex-wrap: wrap;
  }

  .footer__social {
    gap: var(--spacing-xs);
  }

  .footer__social-link {
    width: 40px;
    height: 40px;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.keyboard-navigation *:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .header,
  .footer,
  .btn,
  .nav__toggle {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .main {
    padding-top: 0 !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #00FFFF;
    --secondary-color: #FF00FF;
    --accent-color: #00FF00;
    --text-color: #FFFFFF;
    --text-light: #CCCCCC;
    --background-dark: #000000;
  }
}
